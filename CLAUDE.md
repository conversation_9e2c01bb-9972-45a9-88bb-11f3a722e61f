# Prompt-Snap - CLAUDE.md

## 🎯 项目概述

**Prompt-Snap** 是一个智能提示词优化桌面应用，通过 Vercel AI SDK 集成 Google Gemini 模型，为用户提供个性化的提示词优化服务。直接开发 Tauri 桌面应用，使用 SQLite 作为数据存储。

## 🏗️ 技术架构

### 核心技术栈
- **前端框架**: React 19 + TypeScript + Vite
- **数据存储**: SQLite (Tauri SQL插件)
- **UI组件**: shadcn/ui + Tailwind CSS
- **AI集成**: Vercel AI SDK + Google Provider
- **模板引擎**: nunjucks (Jinja-like)
- **桌面框架**: Tauri v2
- **代码质量**: ESLint + Prettier
- **包管理**: Bun

## 🔄 开发流程

### 核心开发流程
```bash
# 创建Tauri项目
npm create tauri-app@latest prompt-snap

# 选择配置：
# - Package manager: bun
# - UI template: React + TypeScript
# - UI flavor: TypeScript

cd prompt-snap
bun install

# 开发模式
npm run tauri dev

# 构建桌面应用
npm run tauri build
```

### 数据流架构
**UI → Hooks → Services → DAL → SQLite**
- 严格单向数据流
- Zod验证所有边界数据
- 一致的错误处理

## 📋 核心功能模块

### 1. 提示词优化引擎
- **AIService**: Google Gemini API集成
- **OptimizationEngine**: 优化流程协调
- **TemplateEngine**: Jinja模板渲染

### 2. 数据管理
- **DataAccessLayer**: 数据库操作封装
- **DatabaseMigration**: 版本管理和迁移
- **DataIntegrityService**: 数据完整性检查

### 3. 系统集成
- **TauriSystemService**: 全局快捷键、剪贴板、系统托盘
- **WindowManager**: 窗口状态管理
- **BackupService**: 自动备份和恢复

## 🎯 主要任务清单

### 高优先级任务
- [ ] 实现基础提示词优化功能
- [ ] 个人画像管理界面
- [ ] 场景模板系统集成
- [ ] 提示词仓库管理
- [ ] 优化历史记录
- [ ] Tauri系统集成

### 中优先级任务
- [ ] 性能优化（虚拟滚动、查询缓存）
- [ ] 错误处理和用户反馈
- [ ] 数据备份和恢复
- [ ] 跨平台兼容性测试

## 📊 数据模型

### 核心实体
- **Profile**: 个人画像（角色、技能、语调）
- **Scene**: 场景模板（Jinja模板、变量）
- **Prompt**: 提示词（标题、内容、标签）
- **Optimization**: 优化历史（原文、优化结果、元数据）
- **AppSettings**: 应用设置（主题、快捷键、AI配置）

### SQLite数据库结构
```sql
-- 主要表结构
CREATE TABLE profiles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    skills TEXT NOT NULL, -- JSON数组
    tone TEXT NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE scenes (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    template TEXT NOT NULL,
    variables TEXT, -- JSON数组
    category TEXT NOT NULL,
    is_built_in BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE prompts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT, -- JSON数组
    scene_id TEXT,
    profile_id TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);

CREATE TABLE optimizations (
    id TEXT PRIMARY KEY,
    original TEXT NOT NULL,
    optimized TEXT NOT NULL,
    scene_id TEXT,
    profile_id TEXT,
    model_used TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT NOT NULL, -- JSON对象
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);

CREATE TABLE settings (
    id TEXT PRIMARY KEY,
    theme TEXT DEFAULT 'system',
    language TEXT DEFAULT 'zh-CN',
    shortcuts TEXT NOT NULL, -- JSON对象
    ai_config TEXT NOT NULL, -- JSON对象
    auto_save BOOLEAN DEFAULT TRUE,
    backup_enabled BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 全文搜索索引
CREATE VIRTUAL TABLE prompts_fts USING fts5(
    title, content, tags,
    content='prompts',
    content_rowid='rowid'
);
```

## 🛠️ 代码规范

### TypeScript & React
- 使用严格的 TypeScript，为函数参数/返回值提供明确类型
- 仅使用带hooks的函数组件 - 不使用类组件
- 在API边界使用Zod模式进行所有数据验证
- 对性能关键组件使用React.memo()
- 使用ESLint + Prettier进行代码质量保证

### 文件组织
```
src/
├── components/       # React组件 (PascalCase)
├── hooks/           # 自定义hooks (camelCase with use prefix)
├── services/        # 业务逻辑层 (kebab-case)
├── database/        # SQLite数据访问层
├── schemas/         # Zod验证模式
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
├── tauri/           # Tauri系统调用
└── styles/          # 样式文件
```

### ESLint配置
```json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

### 导入顺序
```typescript
// 外部库优先
import React from 'react';
import { z } from 'zod';

// Tauri API
import { invoke } from '@tauri-apps/api/core';

// 内部服务/hooks
import { useDatabase } from '@/hooks/useDatabase';

// 组件
import { Button } from '@/components/ui/button';

// 类型最后
import type { Profile } from '@/types';
```

## 🔍 开发命令

### 基础命令
```bash
# 开发服务器
npm run tauri dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint

# 测试
npm run test

# 构建
npm run tauri build
```

### Tauri特定命令
```bash
# 开发模式
npm run tauri dev

# 构建桌面应用
npm run tauri build

# 调试模式
npm run tauri dev -- --verbose
```

## 🛠️ 配置管理

### Tauri配置文件
```json
// src-tauri/tauri.conf.json
{
  "productName": "Prompt Snap",
  "identifier": "com.promptsnap.app",
  "build": {
    "beforeDevCommand": "bun run dev",
    "beforeBuildCommand": "bun run build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "plugins": {
    "sql": {
      "preload": ["sqlite:prompt_snap.db"]
    }
  }
}
```

### 环境变量
```bash
# AI服务配置
VITE_GOOGLE_API_KEY=your_api_key_here

# 应用配置
VITE_APP_VERSION=1.0.0
VITE_BUILD_ENV=development
```

### 用户设置
- 主题偏好（亮色/暗色/系统）
- 快捷键配置（全局和本地）
- AI模型参数（temperature, maxTokens）
- 自动备份间隔

## 🎨 UI/UX规范

### 设计系统
- **组件库**: shadcn/ui + Tailwind CSS
- **主题**: 亮色/暗色/系统跟随
- **窗口**: 固定尺寸 (1200x800)，最小800x600
- **无障碍**: ARIA标签、键盘导航、屏幕阅读器支持

### 交互模式
- **加载状态**: Skeleton + 进度条
- **错误提示**: Toast通知 + 恢复建议
- **成功反馈**: 确认消息 + 操作结果
- **系统托盘**: 快速操作菜单

## 📱 系统集成

### Tauri配置
```json
// tauri.conf.json
{
  "productName": "Prompt Snap",
  "identifier": "com.promptsnap.app",
  "systemTray": {
    "iconPath": "icons/icon.png",
    "menu": ["快速优化", "显示主窗口", "退出"]
  }
}
```

### 快捷键
- **全局**: 双击Ctrl触发优化
- **应用内**: Ctrl+N 新建提示词
- **搜索**: Ctrl+F 快速搜索

## 🚀 发布流程

### 构建步骤
1. **测试**: 运行完整测试套件
2. **构建**: 生成生产版本
3. **签名**: macOS应用签名和公证
4. **打包**: 创建安装包
5. **分发**: 应用商店或官网下载

### 版本管理
- 语义化版本 (SemVer)
- 变更日志记录
- 自动更新机制

## 📞 支持渠道

### 调试工具
- **开发者工具**: React DevTools
- **数据库查看**: SQLite浏览器
- **性能分析**: Chrome DevTools
- **日志查看**: 应用内日志面板

### 问题反馈
- GitHub Issues
- 应用内反馈表单
- 邮件支持

---

## 🎯 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd prompt-snap
   ```

2. **安装依赖**
   ```bash
   bun install
   ```

3. **启动开发服务器**
   ```bash
   npm run tauri dev
   ```

4. **配置AI服务**
   ```bash
   # 设置Google API Key
   export VITE_GOOGLE_API_KEY=your_api_key_here
   ```

5. **开始开发**
   - 应用启动后自动创建SQLite数据库
   - 访问主界面进行功能开发
   - 使用系统托盘进行快速操作
{"name": "propmt-snap", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@hookform/resolvers": "^5.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-clipboard-manager": "^2.0.0", "@tauri-apps/plugin-global-shortcut": "~2", "@tauri-apps/plugin-sql": "^2.0.0", "@types/react-router-dom": "^5.3.3", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "lucide-react": "^0.533.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.11", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tauri-apps/cli": "^2.7.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}
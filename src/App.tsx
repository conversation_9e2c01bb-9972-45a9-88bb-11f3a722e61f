import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import '@/assets/styles/index.css'
import { OptimizePage } from './pages/OptimizePage';
import { useTheme } from './hooks/useTheme';

function App() {
  useTheme();

  return (
    <Router>
      <Routes>
        <Route path="/" element={<OptimizePage />} />
        <Route path="/profiles" element={<div className="flex h-screen items-center justify-center">个人画像管理（开发中）</div>} />
        <Route path="/scenes" element={<div className="flex h-screen items-center justify-center">场景模板管理（开发中）</div>} />
        <Route path="/prompts" element={<div className="flex h-screen items-center justify-center">提示词仓库（开发中）</div>} />
        <Route path="/history" element={<div className="flex h-screen items-center justify-center">优化历史（开发中）</div>} />
        <Route path="/settings" element={<div className="flex h-screen items-center justify-center">设置（开发中）</div>} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

export default App;

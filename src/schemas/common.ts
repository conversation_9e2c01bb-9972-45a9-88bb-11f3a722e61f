/**
 * 通用验证规则和基础 Schema
 */

import { z } from 'zod';
import { VALIDATION_MESSAGES } from './constants';

// 通用验证规则
export const commonValidations = {
  uuid: z.string().regex(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i, VALIDATION_MESSAGES.INVALID_UUID),
  nonEmptyString: z.string().min(1, VALIDATION_MESSAGES.REQUIRED),
  nonNegativeNumber: z.number().min(0, VALIDATION_MESSAGES.NEGATIVE_NUMBER),
  positiveNumber: z.number().positive(VALIDATION_MESSAGES.POSITIVE_NUMBER_REQUIRED),
  percentage: z.number().min(0, '百分比不能小于0').max(100, '百分比不能大于100'),
};

// 基础实体 Schema
export const BaseEntitySchema = z.object({
  id: commonValidations.uuid,
  createdAt: z.date(),
  updatedAt: z.date(),
});

// 变量 Schema
export const VariableSchema = z.object({
  name: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(50, VALIDATION_MESSAGES.TOO_LONG)
    .regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, '变量名只能包含字母、数字和下划线，且不能以数字开头'),
  description: z.string()
    .max(200, VALIDATION_MESSAGES.TOO_LONG)
    .optional()
    .default(''),
  defaultValue: z.string().optional(),
  required: z.boolean().default(false),
});

export type Variable = z.infer<typeof VariableSchema>;
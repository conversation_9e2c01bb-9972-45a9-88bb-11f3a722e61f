/**
 * 个人画像相关 Schema
 */

import { z } from 'zod';
import { BaseEntitySchema } from './common';
import { FIELD_LIMITS, DEFAULT_VALUES, VALIDATION_MESSAGES } from './constants';

// 个人画像 Schema
export const ProfileSchema = BaseEntitySchema.extend({
  name: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.PROFILE_NAME, VALIDATION_MESSAGES.TOO_LONG),
  role: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.PROFILE_ROLE, VALIDATION_MESSAGES.TOO_LONG),
  skills: z.array(
    z.string()
      .min(1, VALIDATION_MESSAGES.REQUIRED)
      .max(FIELD_LIMITS.PROFILE_SKILL, VALIDATION_MESSAGES.TOO_LONG)
  ).max(FIELD_LIMITS.PROFILE_MAX_SKILLS, VALIDATION_MESSAGES.ARRAY_TOO_LONG),
  tone: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.PROFILE_TONE, VALIDATION_MESSAGES.TOO_LONG),
  isActive: z.boolean().default(DEFAULT_VALUES.PROFILE_ACTIVE),
});

// 创建和更新 Schema
export const CreateProfileSchema = ProfileSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const UpdateProfileSchema = ProfileSchema.omit({ 
  id: true, 
  createdAt: true 
}).partial();

// 表单 Schema（向后兼容）
export const ProfileFormSchema = CreateProfileSchema;

// 类型导出
export type Profile = z.infer<typeof ProfileSchema>;
export type CreateProfile = z.infer<typeof CreateProfileSchema>;
export type UpdateProfile = z.infer<typeof UpdateProfileSchema>;
export type ProfileFormData = CreateProfile;

// 验证函数
export const validateProfile = (data: unknown): Profile => {
  return ProfileSchema.parse(data);
};

export const validatePartialProfile = (data: unknown): Partial<Profile> => {
  return ProfileSchema.partial().parse(data);
};

export const safeValidateProfile = (data: unknown): { success: true; data: Profile } | { success: false; error: z.ZodError } => {
  const result = ProfileSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
};

// 默认值生成器
export const createDefaultProfile = (): CreateProfile => ({
  name: '',
  role: '',
  skills: [],
  tone: '',
  isActive: DEFAULT_VALUES.PROFILE_ACTIVE,
});
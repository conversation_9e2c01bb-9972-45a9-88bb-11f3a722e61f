# Schema 模块化重构

## 概述

本次重构将原本的单一 `schemas/index.ts` 文件拆分为多个模块化文件，提高了代码的可维护性和可读性。

## 文件结构

```
src/schemas/
├── constants.ts          # 常量定义（字段限制、默认值、验证消息等）
├── common.ts            # 通用验证规则和基础 Schema
├── profile.ts           # 个人画像相关 Schema
├── scene.ts             # 场景模板相关 Schema
├── prompt.ts            # 提示词相关 Schema
├── optimization.ts      # 优化历史相关 Schema
├── settings.ts          # 应用设置相关 Schema
├── validation-utils.ts  # 验证工具函数
├── index.ts             # 统一导出文件
├── __tests__/           # 测试文件
│   ├── schema-validation.test.ts
│   └── types-export.test.ts
└── README.md            # 本文档
```

## 主要改进

### 1. 模块化设计
- 每个业务领域的 Schema 独立成文件
- 便于维护和扩展
- 减少文件大小，提高开发体验

### 2. 常量集中管理
- 所有字段限制、默认值、验证消息统一在 `constants.ts` 中管理
- 便于统一修改和维护

### 3. 通用验证规则
- 提取常用的验证规则到 `common.ts`
- 避免重复代码，提高一致性

### 4. 完整的类型导出
- 每个模块导出完整的类型定义
- 包括创建、更新、表单等各种变体类型

### 5. 验证工具函数
- 提供友好的错误处理
- 支持安全验证（不抛出异常）
- 数据清理和格式化功能

## 使用方式

### 基本导入
```typescript
// 导入 Schema
import { ProfileSchema, CreateProfileSchema } from '@/schemas';

// 导入类型
import type { Profile, CreateProfile } from '@/schemas';

// 导入验证函数
import { validateProfile, safeValidateProfile } from '@/schemas';

// 导入常量
import { FIELD_LIMITS, DEFAULT_VALUES } from '@/schemas';
```

### 验证数据
```typescript
// 直接验证（抛出异常）
const profile = validateProfile(data);

// 安全验证（返回结果）
const result = safeValidateProfile(data);
if (result.success) {
  console.log(result.data);
} else {
  console.error(result.error);
}

// 使用验证工具
import { validateWithSchema } from '@/schemas';
const result = validateWithSchema(ProfileSchema, data);
```

### 创建默认值
```typescript
import { createDefaultProfile } from '@/schemas';
const defaultProfile = createDefaultProfile();
```

## 向后兼容性

- 所有原有的导出都保持不变
- 现有代码无需修改即可正常工作
- 保留了表单类型的别名（如 `ProfileFormData`）

## 测试

运行测试以确保所有功能正常：

```bash
bun run test src/schemas/__tests__/
```

## 注意事项

1. 所有 Schema 文件都应该从 `@/schemas` 导入，而不是直接从子模块导入
2. 新增 Schema 时，记得在对应模块文件中添加，并确保在 `index.ts` 中导出
3. 修改常量时，统一在 `constants.ts` 中进行
4. 添加新的验证规则时，考虑是否应该放在 `common.ts` 中复用

## 迁移指南

如果需要从旧的单文件结构迁移：

1. 所有导入路径保持不变（`@/schemas`）
2. 类型名称保持不变
3. 验证函数名称保持不变
4. 常量可能需要从新的路径导入

这次重构提高了代码的组织性和可维护性，同时保持了完全的向后兼容性。
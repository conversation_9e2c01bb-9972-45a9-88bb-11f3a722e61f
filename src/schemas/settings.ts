/**
 * 应用设置相关 Schema
 */

import { z } from 'zod';
import { BaseEntitySchema } from './common';
import { THEMES, DEFAULT_VALUES, AI_LIMITS, VALIDATION_MESSAGES } from './constants';

// 快捷键配置 Schema
export const ShortcutsSchema = z.object({
  optimize: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .regex(/^(CommandOrControl|Ctrl|Cmd|Alt|Shift|\+|[A-Z0-9])+$/i, '快捷键格式不正确'),
  newPrompt: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .regex(/^(CommandOrControl|Ctrl|Cmd|Alt|Shift|\+|[A-Z0-9])+$/i, '快捷键格式不正确'),
  search: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .regex(/^(CommandOrControl|Ctrl|Cmd|Alt|Shift|\+|[A-Z0-9])+$/i, '快捷键格式不正确'),
});

// AI 配置 Schema
export const AIConfigSchema = z.object({
  model: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(50, VALIDATION_MESSAGES.TOO_LONG),
  temperature: z.number()
    .min(AI_LIMITS.MIN_TEMPERATURE)
    .max(AI_LIMITS.MAX_TEMPERATURE),
  maxTokens: z.number()
    .min(AI_LIMITS.MIN_TOKENS)
    .max(AI_LIMITS.MAX_TOKENS)
    .int('最大Token数必须是整数'),
  apiKey: z.string().min(1, 'API密钥不能为空').optional(),
});

// 应用设置 Schema
export const AppSettingsSchema = BaseEntitySchema.extend({
  theme: z.enum(THEMES, {
    message: '主题必须是 light、dark 或 system',
  }).default(DEFAULT_VALUES.THEME),
  language: z.string()
    .min(2, '语言代码不能少于2个字符')
    .max(10, '语言代码不能超过10个字符')
    .default(DEFAULT_VALUES.LANGUAGE),
  shortcuts: ShortcutsSchema,
  aiConfig: AIConfigSchema,
  autoSave: z.boolean().default(DEFAULT_VALUES.AUTO_SAVE),
  backupEnabled: z.boolean().default(DEFAULT_VALUES.BACKUP_ENABLED),
});

// 创建和更新 Schema
export const CreateAppSettingsSchema = AppSettingsSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const UpdateAppSettingsSchema = AppSettingsSchema.omit({ 
  id: true, 
  createdAt: true 
}).partial();

// 表单 Schema（向后兼容）
export const AppSettingsFormSchema = CreateAppSettingsSchema;

// 类型导出
export type AppSettings = z.infer<typeof AppSettingsSchema>;
export type Shortcuts = z.infer<typeof ShortcutsSchema>;
export type AIConfig = z.infer<typeof AIConfigSchema>;
export type CreateAppSettings = z.infer<typeof CreateAppSettingsSchema>;
export type UpdateAppSettings = z.infer<typeof UpdateAppSettingsSchema>;
export type AppSettingsFormData = CreateAppSettings;

// 验证函数
export const validateAppSettings = (data: unknown): AppSettings => {
  return AppSettingsSchema.parse(data);
};

export const validatePartialAppSettings = (data: unknown): Partial<AppSettings> => {
  return AppSettingsSchema.partial().parse(data);
};

export const safeValidateAppSettings = (data: unknown): { success: true; data: AppSettings } | { success: false; error: z.ZodError } => {
  const result = AppSettingsSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
};

// 默认值生成器
export const createDefaultAppSettings = (): CreateAppSettings => ({
  theme: DEFAULT_VALUES.THEME,
  language: DEFAULT_VALUES.LANGUAGE,
  shortcuts: DEFAULT_VALUES.SHORTCUTS,
  aiConfig: {
    model: DEFAULT_VALUES.AI_MODEL,
    temperature: DEFAULT_VALUES.AI_TEMPERATURE,
    maxTokens: DEFAULT_VALUES.AI_MAX_TOKENS,
  },
  autoSave: DEFAULT_VALUES.AUTO_SAVE,
  backupEnabled: DEFAULT_VALUES.BACKUP_ENABLED,
});
/**
 * 提示词相关 Schema
 */

import { z } from 'zod';
import { BaseEntitySchema, commonValidations } from './common';
import { FIELD_LIMITS, DEFAULT_VALUES, VALIDATION_MESSAGES } from './constants';

// 提示词 Schema
export const PromptSchema = BaseEntitySchema.extend({
  title: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.PROMPT_TITLE, VALIDATION_MESSAGES.TOO_LONG),
  content: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.PROMPT_CONTENT, VALIDATION_MESSAGES.TOO_LONG),
  tags: z.array(
    z.string()
      .min(1, VALIDATION_MESSAGES.REQUIRED)
      .max(FIELD_LIMITS.PROMPT_TAG, VALIDATION_MESSAGES.TOO_LONG)
  ).max(FIELD_LIMITS.PROMPT_MAX_TAGS, VALIDATION_MESSAGES.ARRAY_TOO_LONG)
    .default([]),
  sceneId: commonValidations.uuid.optional(),
  profileId: commonValidations.uuid.optional(),
  isFavorite: z.boolean().default(DEFAULT_VALUES.PROMPT_FAVORITE),
});

// 创建和更新 Schema
export const CreatePromptSchema = PromptSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const UpdatePromptSchema = PromptSchema.omit({ 
  id: true, 
  createdAt: true 
}).partial();

// 表单 Schema（向后兼容）
export const PromptFormSchema = CreatePromptSchema;

// 类型导出
export type Prompt = z.infer<typeof PromptSchema>;
export type CreatePrompt = z.infer<typeof CreatePromptSchema>;
export type UpdatePrompt = z.infer<typeof UpdatePromptSchema>;
export type PromptFormData = CreatePrompt;

// 验证函数
export const validatePrompt = (data: unknown): Prompt => {
  return PromptSchema.parse(data);
};

export const validatePartialPrompt = (data: unknown): Partial<Prompt> => {
  return PromptSchema.partial().parse(data);
};

export const safeValidatePrompt = (data: unknown): { success: true; data: Prompt } | { success: false; error: z.ZodError } => {
  const result = PromptSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
};

// 默认值生成器
export const createDefaultPrompt = (): CreatePrompt => ({
  title: '',
  content: '',
  tags: [],
  sceneId: undefined,
  profileId: undefined,
  isFavorite: DEFAULT_VALUES.PROMPT_FAVORITE,
});
/**
 * Schema 相关常量定义
 */

// 字段长度限制
export const FIELD_LIMITS = {
  SHORT_TEXT: 100,
  MEDIUM_TEXT: 500,
  LONG_TEXT: 5000,
  VERY_LONG_TEXT: 10_000,
  PROFILE_NAME: 50,
  PROFILE_ROLE: 100,
  PROFILE_TONE: 100,
  PROFILE_SKILL: 50,
  PROFILE_MAX_SKILLS: 20,
  SCENE_TITLE: 100,
  SCENE_DESCRIPTION: 500,
  SCENE_TEMPLATE: 5000,
  SCENE_CATEGORY: 50,
  SCENE_MAX_VARIABLES: 10,
  PROMPT_TITLE: 200,
  PROMPT_CONTENT: 10_000,
  PROMPT_TAG: 30,
  PROMPT_MAX_TAGS: 10,
  VARIABLE_NAME: 50,
  VARIABLE_DESCRIPTION: 200,
} as const;

// AI 配置限制
export const AI_LIMITS = {
  MIN_TEMPERATURE: 0,
  MAX_TEMPERATURE: 2,
  MIN_TOKENS: 1,
  MAX_TOKENS: 8192,
  MIN_QUALITY: 0,
  MAX_QUALITY: 100,
} as const;

// 默认值
export const DEFAULT_VALUES = {
  THEME: 'system' as const,
  LANGUAGE: 'zh-CN',
  AUTO_SAVE: true,
  BACKUP_ENABLED: true,
  AI_MODEL: 'gemini-1.5-pro',
  AI_TEMPERATURE: 0.7,
  AI_MAX_TOKENS: 2000,
  SHORTCUTS: {
    optimize: 'CommandOrControl+Shift+O',
    newPrompt: 'CommandOrControl+N',
    search: 'CommandOrControl+F',
  },
  PROFILE_ACTIVE: false,
  SCENE_BUILT_IN: false,
  PROMPT_FAVORITE: false,
  VARIABLE_REQUIRED: false,
} as const;

// 支持的主题
export const THEMES = ['light', 'dark', 'system'] as const;
export type Theme = typeof THEMES[number];

// 验证消息
export const VALIDATION_MESSAGES = {
  REQUIRED: '此字段为必填项',
  INVALID_UUID: '无效的 UUID 格式',
  TOO_SHORT: '内容过短',
  TOO_LONG: '内容过长',
  INVALID_NUMBER: '必须是有效数字',
  NEGATIVE_NUMBER: '不能为负数',
  POSITIVE_NUMBER_REQUIRED: '必须是正数',
  INVALID_RANGE: '数值超出有效范围',
  ARRAY_TOO_LONG: '数组长度超出限制',
} as const;
/**
 * 验证工具函数
 * 提供更友好的错误处理和验证结果格式化
 */

import { z } from 'zod';

/**
 * 验证错误类型
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

/**
 * 验证结果类型
 */
export type ValidationResult<T> = 
  | { success: true; data: T; errors: null }
  | { success: false; data: null; errors: ValidationError[] };

/**
 * 将 Zod 错误转换为友好的错误格式
 */
export function formatZodError(error: z.ZodError): ValidationError[] {
  return error.issues.map((err: z.ZodIssue) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
}

/**
 * 通用验证函数
 */
export function validateWithSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: null,
    };
  }
  
  return {
    success: false,
    data: null,
    errors: formatZodError(result.error),
  };
}

/**
 * 验证并抛出友好错误的函数
 */
export function validateOrThrow<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  entityName: string
): T {
  const result = validateWithSchema(schema, data);
  
  if (!result.success) {
    const errorMessages = result.errors.map(err => `${err.field}: ${err.message}`).join(', ');
    throw new Error(`${entityName} 验证失败: ${errorMessages}`);
  }
  
  return result.data;
}

/**
 * 部分验证函数（用于更新操作）
 */
export function validatePartial<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<Partial<T>> {
  // 对于部分验证，我们直接使用原始 schema，但允许部分数据
  const result = schema.safeParse(data);
  
  if (result.success) {
    return {
      success: true,
      data: result.data as Partial<T>,
      errors: null,
    };
  }
  
  return {
    success: false,
    data: null,
    errors: formatZodError(result.error),
  };
}

/**
 * 验证数组的函数
 */
export function validateArray<T>(
  schema: z.ZodSchema<T>,
  data: unknown[],
  entityName: string
): ValidationResult<T[]> {
  const arraySchema = z.array(schema);
  const result = validateWithSchema(arraySchema, data);
  
  if (!result.success) {
    return {
      success: false,
      data: null,
      errors: result.errors.map(err => ({
        ...err,
        field: `${entityName}[${err.field}]`,
      })),
    };
  }
  
  return result;
}

/**
 * 创建自定义验证器
 */
export function createValidator<T>(schema: z.ZodSchema<T>, entityName: string) {
  return {
    validate: (data: unknown): ValidationResult<T> => 
      validateWithSchema(schema, data),
    
    validateOrThrow: (data: unknown): T => 
      validateOrThrow(schema, data, entityName),
    
    validatePartial: (data: unknown): ValidationResult<Partial<T>> => 
      validatePartial(schema, data),
    
    validateArray: (data: unknown[]): ValidationResult<T[]> => 
      validateArray(schema, data, entityName),
  };
}

/**
 * 数据清理函数
 */
export function sanitizeData<T>(data: T): T {
  if (typeof data === 'string') {
    return data.trim() as T;
  }
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeData(item)) as T;
  }
  
  if (data && typeof data === 'object') {
    const sanitized = {} as Record<string, unknown>;
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeData(value);
    }
    return sanitized as T;
  }
  
  return data;
}

/**
 * 验证并清理数据
 */
export function validateAndSanitize<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  entityName: string
): T {
  const sanitized = sanitizeData(data);
  return validateOrThrow(schema, sanitized, entityName);
}
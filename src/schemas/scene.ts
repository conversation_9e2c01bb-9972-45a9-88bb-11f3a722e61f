/**
 * 场景模板相关 Schema
 */

import { z } from 'zod';
import { BaseEntitySchema, VariableSchema } from './common';
import { FIELD_LIMITS, DEFAULT_VALUES, VALIDATION_MESSAGES } from './constants';

// 场景模板 Schema
export const SceneSchema = BaseEntitySchema.extend({
  title: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.SCENE_TITLE, VALIDATION_MESSAGES.TOO_LONG),
  description: z.string()
    .max(FIELD_LIMITS.SCENE_DESCRIPTION, VALIDATION_MESSAGES.TOO_LONG)
    .optional()
    .default(''),
  template: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.SCENE_TEMPLATE, VALIDATION_MESSAGES.TOO_LONG),
  variables: z.array(VariableSchema)
    .max(FIELD_LIMITS.SCENE_MAX_VARIABLES, VALIDATION_MESSAGES.ARRAY_TOO_LONG)
    .default([]),
  category: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.SCENE_CATEGORY, VALIDATION_MESSAGES.TOO_LONG),
  isBuiltIn: z.boolean().default(DEFAULT_VALUES.SCENE_BUILT_IN),
});

// 创建和更新 Schema
export const CreateSceneSchema = SceneSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const UpdateSceneSchema = SceneSchema.omit({ 
  id: true, 
  createdAt: true 
}).partial();

// 表单 Schema（向后兼容）
export const SceneFormSchema = CreateSceneSchema;

// 类型导出
export type Scene = z.infer<typeof SceneSchema>;
export type CreateScene = z.infer<typeof CreateSceneSchema>;
export type UpdateScene = z.infer<typeof UpdateSceneSchema>;
export type SceneFormData = CreateScene;

// 验证函数
export const validateScene = (data: unknown): Scene => {
  return SceneSchema.parse(data);
};

export const validatePartialScene = (data: unknown): Partial<Scene> => {
  return SceneSchema.partial().parse(data);
};

export const safeValidateScene = (data: unknown): { success: true; data: Scene } | { success: false; error: z.ZodError } => {
  const result = SceneSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
};

// 默认值生成器
export const createDefaultScene = (): CreateScene => ({
  title: '',
  description: '',
  template: '',
  variables: [],
  category: '',
  isBuiltIn: DEFAULT_VALUES.SCENE_BUILT_IN,
});
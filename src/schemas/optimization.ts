/**
 * 优化历史相关 Schema
 */

import { z } from 'zod';
import { BaseEntitySchema, commonValidations } from './common';
import { FIELD_LIMITS, AI_LIMITS, VALIDATION_MESSAGES } from './constants';

// 优化元数据 Schema
export const OptimizationMetadataSchema = z.object({
  tokensUsed: commonValidations.nonNegativeNumber.int('Token使用量必须是整数'),
  processingTime: commonValidations.nonNegativeNumber,
  quality: z.number().min(AI_LIMITS.MIN_QUALITY).max(AI_LIMITS.MAX_QUALITY),
  modelVersion: z.string().min(1, '模型版本不能为空').optional(),
});

// 优化历史 Schema
export const OptimizationSchema = BaseEntitySchema.extend({
  original: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.VERY_LONG_TEXT, VALIDATION_MESSAGES.TOO_LONG),
  optimized: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(FIELD_LIMITS.VERY_LONG_TEXT, VALIDATION_MESSAGES.TOO_LONG),
  sceneId: commonValidations.uuid.optional(),
  profileId: commonValidations.uuid.optional(),
  promptId: commonValidations.uuid.optional(),
  modelUsed: z.string()
    .min(1, VALIDATION_MESSAGES.REQUIRED)
    .max(50, VALIDATION_MESSAGES.TOO_LONG),
  timestamp: z.date(),
  metadata: OptimizationMetadataSchema,
});

// 创建 Schema
export const CreateOptimizationSchema = OptimizationSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true,
  timestamp: true 
});

// 表单 Schema（向后兼容）
export const OptimizationFormSchema = CreateOptimizationSchema;

// 类型导出
export type Optimization = z.infer<typeof OptimizationSchema>;
export type OptimizationMetadata = z.infer<typeof OptimizationMetadataSchema>;
export type CreateOptimization = z.infer<typeof CreateOptimizationSchema>;
export type OptimizationFormData = CreateOptimization;

// 验证函数
export const validateOptimization = (data: unknown): Optimization => {
  return OptimizationSchema.parse(data);
};

export const safeValidateOptimization = (data: unknown): { success: true; data: Optimization } | { success: false; error: z.ZodError } => {
  const result = OptimizationSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
};
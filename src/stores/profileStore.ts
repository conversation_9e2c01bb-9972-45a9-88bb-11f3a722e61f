import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Profile } from '@/types';

interface ProfileState {
  // 状态
  profiles: Profile[];
  activeProfile: Profile | null;
  isLoading: boolean;
  
  // 操作
  setProfiles: (profiles: Profile[]) => void;
  addProfile: (profile: Profile) => void;
  updateProfile: (profile: Profile) => void;
  deleteProfile: (id: string) => void;
  setActiveProfile: (profile: Profile | null) => void;
  loadProfiles: () => Promise<void>;
  createProfile: (profile: Omit<Profile, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Profile>;
  updateActiveProfile: (profile: Profile) => void;
}

export const useProfileStore = create<ProfileState>()(
  devtools(
    (set, get) => ({
      // 状态
      profiles: [],
      activeProfile: null,
      isLoading: false,
      
      // 设置所有画像
      setProfiles: (profiles) => set({ profiles }),
      
      // 添加画像
      addProfile: (profile) => set((state) => ({
        profiles: [...state.profiles, profile],
      })),
      
      // 更新画像
      updateProfile: (profile) => set((state) => ({
        profiles: state.profiles.map(p => 
          p.id === profile.id ? profile : p
        ),
        activeProfile: state.activeProfile?.id === profile.id ? profile : state.activeProfile,
      })),
      
      // 删除画像
      deleteProfile: (id) => set((state) => ({
        profiles: state.profiles.filter(p => p.id !== id),
        activeProfile: state.activeProfile?.id === id ? null : state.activeProfile,
      })),
      
      // 设置活跃画像
      setActiveProfile: (profile) => set({ activeProfile: profile }),
      
      // 加载所有画像
      loadProfiles: async () => {
        const { useAppStore } = await import('./appStore');
        const database = useAppStore.getState().database;
        
        if (!database) return;
        
        set({ isLoading: true });
        try {
          const profiles = await database.profiles.getAll();
          const activeProfile = profiles.find(p => p.isActive) || null;
          
          set({ 
            profiles,
            activeProfile,
            isLoading: false,
          });
        } catch (error) {
          console.error('加载个人画像失败:', error);
          set({ isLoading: false });
        }
      },
      
      // 创建新画像
      createProfile: async (profileData) => {
        const { useAppStore } = await import('./appStore');
        const database = useAppStore.getState().database;
        
        if (!database) {
          throw new Error('数据库未初始化');
        }
        
        try {
          const profile = await database.profiles.create(profileData);
          get().addProfile(profile);
          return profile;
        } catch (error) {
          console.error('创建个人画像失败:', error);
          throw error;
        }
      },
      
      // 更新活跃画像
      updateActiveProfile: (profile) => {
        set((state) => ({
          activeProfile: profile,
          profiles: state.profiles.map(p => 
            p.id === profile.id ? profile : p
          ),
        }));
      },
    }),
    {
      name: 'profile-store',
    }
  )
);
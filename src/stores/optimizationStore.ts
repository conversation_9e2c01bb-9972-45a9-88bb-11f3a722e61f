import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Profile } from '@/types';

export interface OptimizationResult {
  id?: string;
  original: string;
  optimized: string;
  profileId?: string;
  modelUsed: string;
  qualityScore?: number;
  improvements?: string[];
  timestamp?: Date;
  metadata?: Record<string, any>;
}

interface OptimizationState {
  // 当前优化会话
  currentPrompt: string;
  isOptimizing: boolean;
  currentResult: OptimizationResult | null;
  
  // 优化历史
  history: OptimizationResult[];
  isLoadingHistory: boolean;
  
  // 优化操作
  setCurrentPrompt: (prompt: string) => void;
  setOptimizing: (optimizing: boolean) => void;
  setCurrentResult: (result: OptimizationResult | null) => void;
  addToHistory: (result: OptimizationResult) => void;
  loadHistory: () => Promise<void>;
  clearHistory: () => void;
  
  // 优化操作
  optimizePrompt: (
    prompt: string,
    profile?: Profile
  ) => Promise<OptimizationResult>;
}

export const useOptimizationStore = create<OptimizationState>()(
  devtools(
    (set, get) => ({
      // 当前优化会话
      currentPrompt: '',
      isOptimizing: false,
      currentResult: null,
      
      // 优化历史
      history: [],
      isLoadingHistory: false,
      
      // 设置当前提示词
      setCurrentPrompt: (prompt) => set({ currentPrompt: prompt }),
      
      // 设置优化状态
      setOptimizing: (optimizing) => set({ isOptimizing: optimizing }),
      
      // 设置当前结果
      setCurrentResult: (result) => set({ currentResult: result }),
      
      // 添加到历史
      addToHistory: (result) => set((state) => ({
        history: [result, ...state.history],
      })),
      
      // 加载历史记录
      loadHistory: async () => {
        const { useAppStore } = await import('./appStore');
        const database = useAppStore.getState().database;
        
        if (!database) return;
        
        set({ isLoadingHistory: true });
        try {
          const history = await database.optimizations.getAll();
          set({ 
            history: history.map(item => ({
              id: item.id,
              original: item.original,
              optimized: item.optimized,
              profileId: item.profileId,
              modelUsed: item.modelUsed,
              timestamp: new Date(item.timestamp),
              metadata: typeof item.metadata === 'string' ? JSON.parse(item.metadata) : item.metadata,
            })),
            isLoadingHistory: false,
          });
        } catch (error) {
          console.error('加载优化历史失败:', error);
          set({ isLoadingHistory: false });
        }
      },
      
      // 清空历史记录
      clearHistory: () => set({ history: [] }),
      
      // 优化提示词
      optimizePrompt: async (prompt: string, profile?: Profile) => {
        const { useAppStore } = await import('./appStore');
        const database = useAppStore.getState().database;
        
        if (!database) {
          throw new Error('数据库未初始化');
        }
        
        set({ isOptimizing: true });
        
        try {
          const optimizationService = await import('@/services/ai/optimization-engine');
          const engine = new optimizationService.OptimizationEngine();
          
          const result = await engine.optimizePrompt(prompt, profile);
          
          // 保存到数据库
          const savedResult = await database.optimizations.create({
            original: prompt,
            optimized: result.optimized,
            profileId: profile?.id,
            modelUsed: 'google-gemini',
            metadata: {
              qualityScore: result.qualityScore,
              improvements: result.improvements,
            },
          });
          
          const optimizationResult: OptimizationResult = {
            ...savedResult,
            timestamp: new Date(savedResult.timestamp),
            metadata: typeof savedResult.metadata === 'string' 
              ? JSON.parse(savedResult.metadata) 
              : savedResult.metadata,
          };
          
          // 添加到历史
          get().addToHistory(optimizationResult);
          set({ currentResult: optimizationResult });
          
          return optimizationResult;
        } catch (error) {
          console.error('优化失败:', error);
          throw error;
        } finally {
          set({ isOptimizing: false });
        }
      },
    }),
    {
      name: 'optimization-store',
    }
  )
);
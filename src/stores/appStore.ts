import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { DatabaseService } from '@/services/database';

export type Theme = 'light' | 'dark' | 'system';

interface AppState {
  // 主题状态
  theme: Theme;
  setTheme: (theme: Theme) => void;
  
  // 数据库状态
  database: DatabaseService | null;
  setDatabase: (database: DatabaseService) => void;
  isDatabaseInitialized: boolean;
  setDatabaseInitialized: (initialized: boolean) => void;
  
  // 全局加载状态
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  
  // 全局错误状态
  error: string | null;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // 主题状态
      theme: 'system' as Theme,
      setTheme: (theme) => set({ theme }),
      
      // 数据库状态
      database: null,
      setDatabase: (database) => set({ database }),
      isDatabaseInitialized: false,
      setDatabaseInitialized: (initialized) => set({ isDatabaseInitialized: initialized }),
      
      // 全局状态
      isLoading: false,
      setLoading: (loading) => set({ isLoading: loading }),
      
      // 错误处理
      error: null,
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'app-store',
      partialize: (state) => ({
        theme: state.theme,
      }),
    }
  )
);
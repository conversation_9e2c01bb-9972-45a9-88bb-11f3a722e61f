# Prompt-Snap 数据库设计文档

## 概述

Prompt-Snap 使用 SQLite 数据库来存储应用数据，包括个人画像、场景模板、提示词、优化历史和应用设置。

## 数据库结构

### 核心表

#### 1. profiles (个人画像表)
存储用户的个人画像信息，用于个性化提示词优化。

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键，UUID 格式 |
| name | TEXT | 画像名称 |
| role | TEXT | 角色描述 |
| skills | TEXT | 技能列表，JSON 数组格式 |
| tone | TEXT | 语调描述 |
| is_active | BOOLEAN | 是否为当前活跃画像 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2. scenes (场景模板表)
存储场景模板，支持变量替换功能。

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键，UUID 格式 |
| title | TEXT | 模板标题 |
| description | TEXT | 模板描述 |
| template | TEXT | 模板内容，支持 {{变量}} 语法 |
| variables | TEXT | 变量定义，JSON 数组格式 |
| category | TEXT | 分类 |
| is_built_in | BOOLEAN | 是否为内置模板 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 3. prompts (提示词表)
存储用户保存的提示词。

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键，UUID 格式 |
| title | TEXT | 提示词标题 |
| content | TEXT | 提示词内容 |
| tags | TEXT | 标签列表，JSON 数组格式 |
| scene_id | TEXT | 关联的场景模板ID（外键） |
| profile_id | TEXT | 关联的个人画像ID（外键） |
| is_favorite | BOOLEAN | 是否收藏 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 4. optimizations (优化历史表)
存储提示词优化的历史记录。

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键，UUID 格式 |
| original | TEXT | 原始提示词 |
| optimized | TEXT | 优化后的提示词 |
| scene_id | TEXT | 使用的场景模板ID（外键） |
| profile_id | TEXT | 使用的个人画像ID（外键） |
| model_used | TEXT | 使用的AI模型 |
| timestamp | DATETIME | 优化时间 |
| metadata | TEXT | 元数据，JSON 格式（包含token使用量、处理时间、质量评分等） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 5. settings (应用设置表)
存储应用的全局设置。

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键，UUID 格式 |
| theme | TEXT | 主题设置（light/dark/system） |
| language | TEXT | 语言设置 |
| shortcuts | TEXT | 快捷键配置，JSON 格式 |
| ai_config | TEXT | AI配置，JSON 格式 |
| auto_save | BOOLEAN | 是否自动保存 |
| backup_enabled | BOOLEAN | 是否启用备份 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 索引设计

#### 性能索引
- `idx_profiles_active`: 快速查找活跃画像
- `idx_profiles_created`: 按创建时间排序
- `idx_scenes_category`: 按分类筛选场景模板
- `idx_prompts_favorite`: 快速查找收藏的提示词
- `idx_optimizations_timestamp`: 按时间排序优化历史

#### 全文搜索索引 (FTS5)
- `prompts_fts`: 提示词全文搜索
- `optimizations_fts`: 优化历史全文搜索

### 触发器

#### 自动更新时间戳
所有表都有自动更新 `updated_at` 字段的触发器。

#### FTS 索引同步
自动保持全文搜索索引与主表数据同步。

### 视图

#### 便捷查询视图
- `active_profiles`: 活跃的个人画像
- `favorite_prompts`: 收藏的提示词
- `built_in_scenes`: 内置场景模板
- `recent_optimizations`: 最近的优化历史

#### 统计视图
- `profile_usage_stats`: 画像使用统计
- `scene_usage_stats`: 场景模板使用统计
- `daily_optimization_stats`: 每日优化统计

## 数据类型约定

### JSON 字段格式

#### skills (技能列表)
```json
["技能1", "技能2", "技能3"]
```

#### variables (变量定义)
```json
[
  {
    "name": "content",
    "description": "要优化的内容",
    "required": true,
    "defaultValue": ""
  }
]
```

#### tags (标签列表)
```json
["标签1", "标签2", "标签3"]
```

#### shortcuts (快捷键配置)
```json
{
  "optimize": "CommandOrControl+Shift+O",
  "newPrompt": "CommandOrControl+N",
  "search": "CommandOrControl+F"
}
```

#### ai_config (AI配置)
```json
{
  "model": "gemini-1.5-pro",
  "temperature": 0.7,
  "maxTokens": 2000,
  "apiKey": "encrypted_api_key"
}
```

#### metadata (优化元数据)
```json
{
  "tokensUsed": 150,
  "processingTime": 1200,
  "quality": 85,
  "modelVersion": "gemini-1.5-pro-001"
}
```

## 迁移管理

### 版本控制
使用 `schema_version` 表跟踪数据库版本：

```sql
CREATE TABLE schema_version (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 迁移文件命名
- `001_initial.sql`: 初始数据库结构
- `002_add_feature.sql`: 添加新功能
- `003_optimize_indexes.sql`: 优化索引

## 性能优化

### 查询优化
1. 使用适当的索引
2. 避免全表扫描
3. 使用 FTS5 进行全文搜索
4. 合理使用视图简化复杂查询

### 存储优化
1. JSON 字段压缩存储
2. 定期清理过期数据
3. 使用 VACUUM 命令优化数据库文件

### 并发控制
1. 启用 WAL 模式提高并发性能
2. 使用事务确保数据一致性
3. 合理设置连接池大小

## 备份策略

### 自动备份
- 每日自动备份数据库文件
- 保留最近30天的备份
- 支持增量备份

### 手动备份
- 用户可随时导出数据
- 支持 JSON 格式导出
- 支持选择性导出（按日期范围、类型等）

## 安全考虑

### 数据加密
- API 密钥等敏感信息加密存储
- 使用 SQLite 的加密扩展（如需要）

### 访问控制
- 通过 Tauri 的权限系统控制数据库访问
- 防止 SQL 注入攻击

### 数据完整性
- 使用外键约束保证引用完整性
- 使用 CHECK 约束验证数据格式
- 使用触发器维护数据一致性
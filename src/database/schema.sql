-- Prompt-Snap 数据库结构
-- SQLite 数据库初始化脚本

-- 启用外键约束和性能优化
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;

-- 个人画像表
CREATE TABLE IF NOT EXISTS profiles (
    id TEXT PRIMARY KEY CHECK (length(id) = 36), -- UUID v4 格式约束
    name TEXT NOT NULL COLLATE NOCASE,
    role TEXT NOT NULL,
    skills TEXT NOT NULL, -- JSON 数组格式存储
    tone TEXT NOT NULL,
    email TEXT, -- 扩展字段：邮箱（可选）
    is_active BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE, -- 软删除标志
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 场景模板表
CREATE TABLE IF NOT EXISTS scenes (
    id TEXT PRIMARY KEY CHECK (length(id) = 36), -- UUID v4 格式约束
    title TEXT NOT NULL COLLATE NOCASE,
    description TEXT DEFAULT '',
    template TEXT NOT NULL,
    variables TEXT DEFAULT '[]', -- JSON 数组格式存储变量定义
    category TEXT NOT NULL,
    is_built_in BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE, -- 软删除标志
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 提示词表
CREATE TABLE IF NOT EXISTS prompts (
    id TEXT PRIMARY KEY CHECK (length(id) = 36), -- UUID v4 格式约束
    title TEXT NOT NULL COLLATE NOCASE,
    content TEXT NOT NULL,
    tags TEXT DEFAULT '[]', -- JSON 数组格式存储标签
    scene_id TEXT,
    profile_id TEXT,
    usage_count INTEGER DEFAULT 0, -- 使用频率统计
    is_favorite BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE, -- 软删除标志
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);

-- 优化历史表
CREATE TABLE IF NOT EXISTS optimizations (
    id TEXT PRIMARY KEY CHECK (length(id) = 36), -- UUID v4 格式约束
    original TEXT NOT NULL,
    optimized TEXT NOT NULL,
    scene_id TEXT,
    profile_id TEXT,
    prompt_id TEXT, -- 反向关联到提示词（可选）
    model_used TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT NOT NULL, -- JSON 对象格式存储元数据
    is_deleted BOOLEAN DEFAULT FALSE, -- 软删除标志
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL,
    FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE SET NULL
);

-- 应用设置表（单例模式）
CREATE TABLE IF NOT EXISTS settings (
    id TEXT PRIMARY KEY DEFAULT 'app', -- 固定为 'app'，单例模式
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT DEFAULT 'zh-CN',
    shortcuts TEXT NOT NULL, -- JSON 对象格式存储快捷键配置
    ai_config TEXT NOT NULL, -- JSON 对象格式存储AI配置
    auto_save BOOLEAN DEFAULT TRUE,
    backup_enabled BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能

-- 个人画像索引
CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles(is_active) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_profiles_created ON profiles(created_at DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_profiles_name ON profiles(name COLLATE NOCASE) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email) WHERE email IS NOT NULL AND is_deleted = FALSE;

-- 场景模板索引
CREATE INDEX IF NOT EXISTS idx_scenes_category ON scenes(category) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_scenes_built_in ON scenes(is_built_in) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_scenes_created ON scenes(created_at DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_scenes_title ON scenes(title COLLATE NOCASE) WHERE is_deleted = FALSE;

-- 提示词索引
CREATE INDEX IF NOT EXISTS idx_prompts_favorite ON prompts(is_favorite) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_prompts_created ON prompts(created_at DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_prompts_scene_profile ON prompts(scene_id, profile_id) WHERE is_deleted = FALSE; -- 组合索引
CREATE INDEX IF NOT EXISTS idx_prompts_usage ON prompts(usage_count DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_prompts_title ON prompts(title COLLATE NOCASE) WHERE is_deleted = FALSE;

-- 优化历史索引
CREATE INDEX IF NOT EXISTS idx_optimizations_timestamp_profile ON optimizations(timestamp DESC, profile_id) WHERE is_deleted = FALSE; -- 复合索引
CREATE INDEX IF NOT EXISTS idx_optimizations_scene ON optimizations(scene_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_optimizations_model ON optimizations(model_used) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_optimizations_prompt ON optimizations(prompt_id) WHERE is_deleted = FALSE;

-- 全文搜索索引 (FTS5) - 优化版本
-- 为提示词内容创建全文搜索索引
CREATE VIRTUAL TABLE IF NOT EXISTS prompts_fts USING fts5(
    title, 
    content, 
    tags UNINDEXED, -- tags 不需要单独索引，减少存储空间
    content='prompts',
    content_rowid='rowid'
);

-- 为优化历史创建全文搜索索引
CREATE VIRTUAL TABLE IF NOT EXISTS optimizations_fts USING fts5(
    original,
    optimized,
    content='optimizations',
    content_rowid='rowid'
);

-- 触发器：保持 FTS 索引与主表同步

-- 提示词 FTS 同步触发器
CREATE TRIGGER IF NOT EXISTS prompts_fts_insert AFTER INSERT ON prompts BEGIN
    INSERT INTO prompts_fts(rowid, title, content, tags) 
    VALUES (new.rowid, new.title, new.content, new.tags);
END;

CREATE TRIGGER IF NOT EXISTS prompts_fts_delete AFTER DELETE ON prompts BEGIN
    DELETE FROM prompts_fts WHERE rowid = old.rowid;
END;

CREATE TRIGGER IF NOT EXISTS prompts_fts_update AFTER UPDATE ON prompts BEGIN
    DELETE FROM prompts_fts WHERE rowid = old.rowid;
    INSERT INTO prompts_fts(rowid, title, content, tags) 
    VALUES (new.rowid, new.title, new.content, new.tags);
END;

-- 优化历史 FTS 同步触发器
CREATE TRIGGER IF NOT EXISTS optimizations_fts_insert AFTER INSERT ON optimizations BEGIN
    INSERT INTO optimizations_fts(rowid, original, optimized) 
    VALUES (new.rowid, new.original, new.optimized);
END;

CREATE TRIGGER IF NOT EXISTS optimizations_fts_delete AFTER DELETE ON optimizations BEGIN
    DELETE FROM optimizations_fts WHERE rowid = old.rowid;
END;

CREATE TRIGGER IF NOT EXISTS optimizations_fts_update AFTER UPDATE ON optimizations BEGIN
    DELETE FROM optimizations_fts WHERE rowid = old.rowid;
    INSERT INTO optimizations_fts(rowid, original, optimized) 
    VALUES (new.rowid, new.original, new.optimized);
END;

-- 自动更新 updated_at 字段的触发器（优化版本 - 使用 BEFORE UPDATE）

-- 个人画像更新触发器
CREATE TRIGGER IF NOT EXISTS profiles_updated_at 
BEFORE UPDATE ON profiles 
FOR EACH ROW
BEGIN
    UPDATE profiles SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 场景模板更新触发器
CREATE TRIGGER IF NOT EXISTS scenes_updated_at 
BEFORE UPDATE ON scenes 
FOR EACH ROW
BEGIN
    UPDATE scenes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 提示词更新触发器
CREATE TRIGGER IF NOT EXISTS prompts_updated_at 
BEFORE UPDATE ON prompts 
FOR EACH ROW
BEGIN
    UPDATE prompts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 优化历史更新触发器
CREATE TRIGGER IF NOT EXISTS optimizations_updated_at 
BEFORE UPDATE ON optimizations 
FOR EACH ROW
BEGIN
    UPDATE optimizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 应用设置更新触发器
CREATE TRIGGER IF NOT EXISTS settings_updated_at 
BEFORE UPDATE ON settings 
FOR EACH ROW
BEGIN
    UPDATE settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 数据完整性约束

-- 确保每个用户只有一个活跃的画像（可选约束，根据需求决定是否启用）
-- CREATE UNIQUE INDEX IF NOT EXISTS idx_profiles_single_active 
-- ON profiles(is_active) WHERE is_active = TRUE AND is_deleted = FALSE;

-- 确保设置表只有一条记录（单例模式）
CREATE UNIQUE INDEX IF NOT EXISTS idx_settings_singleton ON settings(id);

-- 视图：简化常用查询（优化版本 - 排除软删除记录）

-- 活跃画像视图
CREATE VIEW IF NOT EXISTS active_profiles AS
SELECT * FROM profiles WHERE is_active = TRUE AND is_deleted = FALSE;

-- 收藏提示词视图
CREATE VIEW IF NOT EXISTS favorite_prompts AS
SELECT * FROM prompts WHERE is_favorite = TRUE AND is_deleted = FALSE;

-- 内置场景模板视图
CREATE VIEW IF NOT EXISTS built_in_scenes AS
SELECT * FROM scenes WHERE is_built_in = TRUE AND is_deleted = FALSE;

-- 最近优化历史视图（最近30天，限制1000条记录）
CREATE VIEW IF NOT EXISTS recent_optimizations AS
SELECT * FROM optimizations 
WHERE timestamp >= datetime('now', '-30 days') AND is_deleted = FALSE
ORDER BY timestamp DESC
LIMIT 1000;

-- 每个画像的收藏提示词视图
CREATE VIEW IF NOT EXISTS favorite_prompts_per_profile AS
SELECT 
    p.id as profile_id,
    p.name as profile_name,
    pr.*
FROM profiles p
JOIN prompts pr ON p.id = pr.profile_id
WHERE pr.is_favorite = TRUE AND pr.is_deleted = FALSE AND p.is_deleted = FALSE;

-- 统计视图

-- 画像使用统计
CREATE VIEW IF NOT EXISTS profile_usage_stats AS
SELECT 
    p.id,
    p.name,
    COUNT(o.id) as optimization_count,
    COUNT(pr.id) as prompt_count,
    MAX(o.timestamp) as last_used
FROM profiles p
LEFT JOIN optimizations o ON p.id = o.profile_id
LEFT JOIN prompts pr ON p.id = pr.profile_id
GROUP BY p.id, p.name;

-- 场景模板使用统计
CREATE VIEW IF NOT EXISTS scene_usage_stats AS
SELECT 
    s.id,
    s.title,
    s.category,
    COUNT(o.id) as optimization_count,
    COUNT(pr.id) as prompt_count,
    MAX(o.timestamp) as last_used
FROM scenes s
LEFT JOIN optimizations o ON s.id = o.scene_id
LEFT JOIN prompts pr ON s.id = pr.scene_id
GROUP BY s.id, s.title, s.category;

-- 每日优化统计
CREATE VIEW IF NOT EXISTS daily_optimization_stats AS
SELECT 
    DATE(timestamp) as date,
    COUNT(*) as count,
    AVG(CAST(JSON_EXTRACT(metadata, '$.quality') AS REAL)) as avg_quality,
    SUM(CAST(JSON_EXTRACT(metadata, '$.tokensUsed') AS INTEGER)) as total_tokens
FROM optimizations
GROUP BY DATE(timestamp)
ORDER BY date DESC;
-- 迁移 003: 添加用户偏好设置
-- 描述: 扩展设置表以支持更多用户偏好

-- 向上迁移 (UP)
-- 为 settings 表添加新的偏好字段
ALTER TABLE settings ADD COLUMN ui_preferences TEXT DEFAULT '{}'; -- JSON 格式存储 UI 偏好
ALTER TABLE settings ADD COLUMN notification_settings TEXT DEFAULT '{}'; -- 通知设置
ALTER TABLE settings ADD COLUMN privacy_settings TEXT DEFAULT '{}'; -- 隐私设置
ALTER TABLE settings ADD COLUMN export_preferences TEXT DEFAULT '{}'; -- 导出偏好

-- 创建用户活动日志表
CREATE TABLE IF NOT EXISTS user_activity_log (
    id TEXT PRIMARY KEY,
    action_type TEXT NOT NULL, -- 'create', 'update', 'delete', 'optimize', 'search'
    entity_type TEXT NOT NULL, -- 'profile', 'scene', 'prompt', 'optimization'
    entity_id TEXT,
    details TEXT, -- JSON 格式存储详细信息
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    session_id TEXT, -- 会话标识
    user_agent TEXT -- 用户代理信息（如果适用）
);

-- 创建活动日志索引
CREATE INDEX IF NOT EXISTS idx_activity_log_timestamp ON user_activity_log(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_activity_log_action_type ON user_activity_log(action_type);
CREATE INDEX IF NOT EXISTS idx_activity_log_entity_type ON user_activity_log(entity_type);
CREATE INDEX IF NOT EXISTS idx_activity_log_session ON user_activity_log(session_id);

-- 创建活动统计视图
CREATE VIEW IF NOT EXISTS daily_activity_stats AS
SELECT 
    DATE(timestamp) as date,
    action_type,
    entity_type,
    COUNT(*) as count
FROM user_activity_log
GROUP BY DATE(timestamp), action_type, entity_type
ORDER BY date DESC;

-- 向下迁移 (DOWN)
-- DROP VIEW IF EXISTS daily_activity_stats;
-- DROP TABLE IF EXISTS user_activity_log;
-- 对于 settings 表的新字段，需要重建表来移除
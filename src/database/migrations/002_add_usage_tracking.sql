-- 迁移 002: 添加使用跟踪功能
-- 描述: 为提示词和优化记录添加使用统计字段

-- 向上迁移 (UP)
-- 为 prompts 表添加使用统计字段（如果不存在）
ALTER TABLE prompts ADD COLUMN usage_count INTEGER DEFAULT 0;
ALTER TABLE prompts ADD COLUMN last_used_at DATETIME;

-- 为 optimizations 表添加性能指标
ALTER TABLE optimizations ADD COLUMN processing_time_ms INTEGER DEFAULT 0;
ALTER TABLE optimizations ADD COLUMN tokens_per_second REAL DEFAULT 0.0;

-- 创建使用统计视图
CREATE VIEW IF NOT EXISTS usage_statistics AS
SELECT 
    'prompts' as table_name,
    COUNT(*) as total_records,
    SUM(usage_count) as total_usage,
    AVG(usage_count) as avg_usage,
    MAX(last_used_at) as last_activity
FROM prompts WHERE is_deleted = FALSE
UNION ALL
SELECT 
    'optimizations' as table_name,
    COUNT(*) as total_records,
    COUNT(*) as total_usage,
    AVG(processing_time_ms) as avg_usage,
    MAX(timestamp) as last_activity
FROM optimizations WHERE is_deleted = FALSE;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_prompts_usage_count ON prompts(usage_count DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_prompts_last_used ON prompts(last_used_at DESC) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_optimizations_processing_time ON optimizations(processing_time_ms) WHERE is_deleted = FALSE;

-- 向下迁移 (DOWN)
-- 注意: SQLite 不支持 DROP COLUMN，所以我们需要重建表
-- 这里只是记录回滚步骤，实际实现会在代码中处理

-- DROP VIEW IF EXISTS usage_statistics;
-- DROP INDEX IF EXISTS idx_prompts_usage_count;
-- DROP INDEX IF EXISTS idx_prompts_last_used;
-- DROP INDEX IF EXISTS idx_optimizations_processing_time;

-- 对于 SQLite，回滚需要重建表，这将在迁移代码中处理
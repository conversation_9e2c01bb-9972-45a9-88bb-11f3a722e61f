-- 初始数据库迁移
-- 版本: 1
-- 描述: 创建初始数据库结构和默认数据

-- 执行主要的 schema 创建
-- (这个文件的内容与 schema.sql 相同，但用于迁移管理)

-- 插入默认数据

-- 插入默认个人画像
INSERT OR IGNORE INTO profiles (
    id, 
    name, 
    role, 
    skills, 
    tone, 
    is_active,
    created_at,
    updated_at
) VALUES 
(
    'default-profile-001',
    '通用助手',
    '智能AI助手',
    '["文本处理", "内容优化", "逻辑分析", "创意写作"]',
    '专业、友好、准确',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'default-profile-002',
    '技术专家',
    '软件开发专家',
    '["编程", "技术文档", "代码审查", "架构设计"]',
    '技术性强、逻辑清晰、简洁明了',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'default-profile-003',
    '创意写手',
    '内容创作专家',
    '["创意写作", "文案策划", "品牌传播", "故事叙述"]',
    '富有创意、生动有趣、引人入胜',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 插入默认场景模板
INSERT OR IGNORE INTO scenes (
    id,
    title,
    description,
    template,
    variables,
    category,
    is_built_in,
    created_at,
    updated_at
) VALUES
(
    'scene-code-001',
    '代码优化',
    '优化编程相关的提示词，使其更加精确和专业',
    '请优化以下代码相关的提示词，使其更加精确和专业。要求：1. 明确技术要求 2. 提供具体示例 3. 考虑最佳实践\n\n原始提示词：{{content}}',
    '[{"name": "content", "description": "要优化的提示词内容", "required": true}]',
    '编程',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'scene-writing-001',
    '文案创作',
    '优化文案和营销相关的提示词，使其更具吸引力和说服力',
    '请优化以下文案提示词，使其更具吸引力和说服力。要求：1. 突出核心价值 2. 增强情感共鸣 3. 明确行动指引\n\n原始提示词：{{content}}',
    '[{"name": "content", "description": "要优化的提示词内容", "required": true}]',
    '文案',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'scene-analysis-001',
    '数据分析',
    '优化数据分析相关的提示词，提高分析的准确性和深度',
    '请优化以下数据分析提示词，提高分析的准确性和深度。要求：1. 明确分析目标 2. 指定分析方法 3. 要求具体结论\n\n原始提示词：{{content}}',
    '[{"name": "content", "description": "要优化的提示词内容", "required": true}]',
    '分析',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'scene-education-001',
    '教学辅导',
    '优化教育和学习相关的提示词，提高教学效果',
    '请优化以下教学提示词，提高教学效果。要求：1. 循序渐进的教学方式 2. 提供实例和练习 3. 考虑学习者水平\n\n原始提示词：{{content}}',
    '[{"name": "content", "description": "要优化的提示词内容", "required": true}]',
    '教育',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 插入默认应用设置
INSERT OR IGNORE INTO settings (
    id,
    theme,
    language,
    shortcuts,
    ai_config,
    auto_save,
    backup_enabled,
    created_at,
    updated_at
) VALUES (
    'default-settings-001',
    'system',
    'zh-CN',
    '{"optimize": "CommandOrControl+Shift+O", "newPrompt": "CommandOrControl+N", "search": "CommandOrControl+F"}',
    '{"model": "gemini-1.5-pro", "temperature": 0.7, "maxTokens": 2000}',
    TRUE,
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 创建数据库版本表（用于迁移管理）
CREATE TABLE IF NOT EXISTS schema_version (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 记录当前迁移版本
INSERT OR IGNORE INTO schema_version (version, description) 
VALUES (1, '初始数据库结构和默认数据');
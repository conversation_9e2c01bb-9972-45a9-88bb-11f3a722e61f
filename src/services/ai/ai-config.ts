/**
 * AI 配置管理器
 * 负责 AI 服务的配置管理、密钥存储和验证
 */

import type { AIConfig } from './ai-service';
import { AIService } from './ai-service';

// 配置存储键名
const CONFIG_STORAGE_KEY = 'ai_config';
const API_KEY_STORAGE_KEY = 'ai_api_key';

// 默认配置
const DEFAULT_CONFIG: Omit<AIConfig, 'apiKey'> = {
  model: 'gemini-1.5-pro',
  temperature: 0.7,
  maxTokens: 2000,
};

export class AIConfigManager {
  private config: AIConfig | null = null;
  private listeners: Array<(config: AIConfig | null) => void> = [];

  constructor() {
    this.loadConfig();
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      // 从本地存储加载配置
      const savedConfig = localStorage.getItem(CONFIG_STORAGE_KEY);
      const savedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);

      if (savedConfig && savedApiKey) {
        const config = JSON.parse(savedConfig);
        this.config = {
          ...DEFAULT_CONFIG,
          ...config,
          apiKey: savedApiKey,
        };
      }
    } catch (error) {
      console.warn('加载 AI 配置失败:', error);
      this.config = null;
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    if (!this.config) return;

    try {
      // 分离 API 密钥和其他配置
      const { apiKey, ...otherConfig } = this.config;
      
      localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(otherConfig));
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
    } catch (error) {
      console.error('保存 AI 配置失败:', error);
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): AIConfig | null {
    return this.config;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AIConfig>): void {
    if (this.config) {
      this.config = { ...this.config, ...newConfig };
    } else {
      this.config = { ...DEFAULT_CONFIG, apiKey: '', ...newConfig };
    }

    this.saveConfig();
    this.notifyListeners();
  }

  /**
   * 设置 API 密钥
   */
  setApiKey(apiKey: string): { success: boolean; error?: string } {
    // 验证 API 密钥
    const validation = AIService.validateApiKey(apiKey);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // 更新配置
    this.updateConfig({ apiKey });
    
    return { success: true };
  }

  /**
   * 获取 API 密钥（脱敏显示）
   */
  getMaskedApiKey(): string {
    if (!this.config?.apiKey) return '';
    
    const key = this.config.apiKey;
    if (key.length <= 8) return key;
    
    return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
  }

  /**
   * 检查配置是否完整
   */
  isConfigured(): boolean {
    return !!(this.config?.apiKey && this.config.model);
  }

  /**
   * 重置配置
   */
  resetConfig(): void {
    this.config = null;
    
    try {
      localStorage.removeItem(CONFIG_STORAGE_KEY);
      localStorage.removeItem(API_KEY_STORAGE_KEY);
    } catch (error) {
      console.error('清除 AI 配置失败:', error);
    }

    this.notifyListeners();
  }

  /**
   * 测试当前配置
   */
  async testCurrentConfig(): Promise<{
    success: boolean;
    error?: string;
    latency?: number;
  }> {
    if (!this.config) {
      return { success: false, error: '配置未设置' };
    }

    try {
      const aiService = new AIService(this.config);
      return await aiService.testConnection();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 导出配置（不包含 API 密钥）
   */
  exportConfig(): Omit<AIConfig, 'apiKey'> | null {
    if (!this.config) return null;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { apiKey, ...exportConfig } = this.config;
    return exportConfig;
  }

  /**
   * 导入配置
   */
  importConfig(config: Omit<AIConfig, 'apiKey'>): void {
    if (this.config) {
      this.updateConfig(config);
    } else {
      this.config = { ...config, apiKey: '' };
      this.saveConfig();
      this.notifyListeners();
    }
  }

  /**
   * 添加配置变更监听器
   */
  addListener(listener: (config: AIConfig | null) => void): () => void {
    this.listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        console.error('AI 配置监听器执行失败:', error);
      }
    });
  }

  /**
   * 获取配置建议
   */
  getConfigRecommendations(): {
    model: string;
    temperature: number;
    maxTokens: number;
    reasoning: string;
  }[] {
    return [
      {
        model: 'gemini-1.5-pro',
        temperature: 0.7,
        maxTokens: 2000,
        reasoning: '平衡创造性和准确性，适合大多数优化任务',
      },
      {
        model: 'gemini-1.5-flash',
        temperature: 0.5,
        maxTokens: 1500,
        reasoning: '快速响应，适合简单的优化任务',
      },
      {
        model: 'gemini-1.5-pro',
        temperature: 0.9,
        maxTokens: 3000,
        reasoning: '高创造性，适合创意写作和头脑风暴',
      },
      {
        model: 'gemini-1.5-pro',
        temperature: 0.3,
        maxTokens: 1000,
        reasoning: '高准确性，适合技术文档和分析任务',
      },
    ];
  }

  /**
   * 根据使用场景推荐配置
   */
  getScenarioRecommendation(scenario: 'creative' | 'technical' | 'general' | 'fast'): Partial<AIConfig> {
    switch (scenario) {
      case 'creative':
        return {
          model: 'gemini-1.5-pro',
          temperature: 0.9,
          maxTokens: 3000,
        };
      
      case 'technical':
        return {
          model: 'gemini-1.5-pro',
          temperature: 0.3,
          maxTokens: 2000,
        };
      
      case 'fast':
        return {
          model: 'gemini-1.5-flash',
          temperature: 0.5,
          maxTokens: 1500,
        };
      
      case 'general':
      default:
        return DEFAULT_CONFIG;
    }
  }

  /**
   * 验证配置参数
   */
  validateConfig(config: Partial<AIConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证模型
    if (config.model) {
      const supportedModels = AIService.getSupportedModels();
      if (!supportedModels.some(m => m.id === config.model)) {
        errors.push('不支持的模型');
      }
    }

    // 验证温度参数
    if (config.temperature !== undefined) {
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push('温度参数必须在 0-2 之间');
      }
    }

    // 验证最大 Token 数
    if (config.maxTokens !== undefined) {
      if (config.maxTokens < 1 || config.maxTokens > 8192) {
        errors.push('最大 Token 数必须在 1-8192 之间');
      }
    }

    // 验证 API 密钥
    if (config.apiKey) {
      const keyValidation = AIService.validateApiKey(config.apiKey);
      if (!keyValidation.valid) {
        errors.push(keyValidation.error || 'API 密钥无效');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// 导出单例实例
export const aiConfigManager = new AIConfigManager();
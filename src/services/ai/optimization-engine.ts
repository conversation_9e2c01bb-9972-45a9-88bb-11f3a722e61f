/**
 * 优化引擎
 * 协调提示词优化的完整流程
 */

import { getAIService } from './ai-factory';
import { dal } from '@/services/database';
import type { Profile, CreateOptimization } from '@/schemas';
import type { OptimizationRequest, OptimizationResult } from './ai-service';

// 优化选项接口
export interface OptimizationOptions {
  // 基础选项
  originalPrompt: string;
  profileId?: string;
  
  // 优化参数
  context?: string;
  requirements?: string[];
  
  // 控制选项
  saveToHistory?: boolean;
  generateAlternatives?: boolean;
  includeAnalysis?: boolean;
}

// 扩展的优化结果
export interface EnhancedOptimizationResult extends OptimizationResult {
  // 原始信息
  originalPrompt: string;
  profile?: Profile;
  
  // 分析结果
  analysis?: {
    originalScore: number;
    originalStrengths: string[];
    originalWeaknesses: string[];
    improvementAreas: string[];
  };
  
  // 替代方案
  alternatives?: Array<{
    prompt: string;
    focus: string;
    score: number;
  }>;
  
  // 历史记录 ID
  historyId?: string;
}

// 质量评估结果
export interface QualityAssessment {
  score: number;
  factors: {
    clarity: number;      // 清晰度 (0-100)
    specificity: number;  // 具体性 (0-100)
    structure: number;    // 结构性 (0-100)
    completeness: number; // 完整性 (0-100)
    actionability: number; // 可执行性 (0-100)
  };
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
}

export class OptimizationEngine {
  /**
   * 执行完整的优化流程
   */
  async optimize(options: OptimizationOptions): Promise<EnhancedOptimizationResult> {
    try {
      // 1. 获取 AI 服务
      const aiService = getAIService();
      if (!aiService) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      // 2. 获取个人画像（如果指定）
      let profile: Profile | undefined;
      if (options.profileId) {
        const profileResult = await dal.profiles.getById(options.profileId);
        if (!profileResult) {
          throw new Error('指定的个人画像不存在');
        }
        profile = profileResult;
      }

      // 3. 分析原始提示词（如果需要）
      let analysis: EnhancedOptimizationResult['analysis'];
      if (options.includeAnalysis) {
        const qualityAssessment = await this.analyzePrompt(options.originalPrompt);
        analysis = {
          originalScore: qualityAssessment.score,
          originalStrengths: qualityAssessment.strengths,
          originalWeaknesses: qualityAssessment.weaknesses,
          improvementAreas: qualityAssessment.suggestions,
        };
      }

      // 4. 构建优化请求
      const optimizationRequest: OptimizationRequest = {
        originalPrompt: options.originalPrompt,
        profile,
        context: options.context,
        requirements: options.requirements,
      };

      // 5. 执行主要优化
      const optimizationResult = await aiService.optimizePrompt(optimizationRequest);

      // 6. 生成替代方案（如果需要）
      let alternatives: EnhancedOptimizationResult['alternatives'];
      if (options.generateAlternatives) {
        alternatives = await this.generateAlternatives(optimizationRequest);
      }

      // 7. 构建增强结果
      const enhancedResult: EnhancedOptimizationResult = {
        ...optimizationResult,
        originalPrompt: options.originalPrompt,
        profile,
        analysis,
        alternatives,
      };

      // 8. 保存到历史记录（如果需要）
      if (options.saveToHistory !== false) { // 默认保存
        const historyId = await this.saveToHistory(enhancedResult, options);
        enhancedResult.historyId = historyId;
      }

      return enhancedResult;
    } catch (error) {
      throw new Error(`优化流程执行失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析提示词质量
   */
  async analyzePrompt(prompt: string): Promise<QualityAssessment> {
    const aiService = getAIService();
    if (!aiService) {
      throw new Error('AI 服务未配置');
    }

    try {
      const analysis = await aiService.analyzePrompt(prompt);
      
      // 计算各个因子的分数（基于分析结果）
      const factors = this.calculateQualityFactors(prompt, analysis);
      
      return {
        score: analysis.score,
        factors,
        strengths: analysis.strengths,
        weaknesses: analysis.weaknesses,
        suggestions: analysis.suggestions,
      };
    } catch (error) {
      throw new Error(`提示词分析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 生成替代优化方案
   */
  private async generateAlternatives(
    baseRequest: OptimizationRequest
  ): Promise<Array<{ prompt: string; focus: string; score: number }>> {
    const aiService = getAIService();
    if (!aiService) {
      return [];
    }

    const focuses = [
      { name: '简洁性', description: '使提示词更加简洁明了' },
      { name: '创造性', description: '增强创造性和想象力' },
      { name: '技术性', description: '强调技术细节和准确性' },
    ];

    const alternatives = [];

    for (const focus of focuses) {
      try {
        const alternativeRequest: OptimizationRequest = {
          ...baseRequest,
          requirements: [
            ...(baseRequest.requirements || []),
            `重点关注${focus.description}`,
          ],
        };

        const result = await aiService.optimizePrompt(alternativeRequest);
        
        alternatives.push({
          prompt: result.optimizedPrompt,
          focus: focus.name,
          score: result.quality,
        });

        // 添加延迟避免 API 限制
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.warn(`生成${focus.name}替代方案失败:`, error);
      }
    }

    return alternatives;
  }

  /**
   * 计算质量因子分数
   */
  private calculateQualityFactors(
    prompt: string,
    analysis: { score: number; strengths: string[]; weaknesses: string[] }
  ): QualityAssessment['factors'] {
    // 基于提示词长度和结构的启发式评估
    const length = prompt.length;
    const sentences = prompt.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = prompt.split(/\s+/).filter(w => w.length > 0);
    
    // 清晰度：基于句子结构和长度
    const clarity = Math.min(100, Math.max(0, 
      80 - Math.abs(sentences.length - 3) * 10 + 
      (sentences.every(s => s.length < 100) ? 20 : 0)
    ));

    // 具体性：基于具体词汇和细节
    const specificWords = ['具体', '详细', '例如', '包括', '特别是', '确保'];
    const specificityBonus = specificWords.reduce((count, word) => 
      count + (prompt.includes(word) ? 1 : 0), 0) * 5;
    const specificity = Math.min(100, 60 + specificityBonus);

    // 结构性：基于段落和逻辑结构
    const hasStructure = prompt.includes('\n') || prompt.includes('：') || prompt.includes('1.') || prompt.includes('-');
    const structure = hasStructure ? 80 : 60;

    // 完整性：基于内容长度和覆盖面
    const completeness = Math.min(100, Math.max(30, 
      (length > 50 ? 70 : 50) + (words.length > 20 ? 20 : 0) + (sentences.length > 2 ? 10 : 0)
    ));

    // 可执行性：基于动词和指令性语言
    const actionWords = ['请', '帮助', '生成', '创建', '分析', '优化', '改进'];
    const actionBonus = actionWords.reduce((count, word) => 
      count + (prompt.includes(word) ? 1 : 0), 0) * 8;
    const actionability = Math.min(100, 50 + actionBonus);

    return {
      clarity: Math.round(clarity),
      specificity: Math.round(specificity),
      structure: Math.round(structure),
      completeness: Math.round(completeness),
      actionability: Math.round(actionability),
    };
  }

  /**
   * 保存优化结果到历史记录
   */
  private async saveToHistory(
    result: EnhancedOptimizationResult,
    options: OptimizationOptions
  ): Promise<string> {
    try {
      const optimizationData: CreateOptimization = {
        original: result.originalPrompt,
        optimized: result.optimizedPrompt,
        profileId: options.profileId,
        modelUsed: 'gemini-1.5-pro', // 从配置中获取
        metadata: {
          tokensUsed: result.tokensUsed,
          processingTime: result.processingTime,
          quality: result.quality,
        },
      };

      const savedOptimization = await dal.optimizations.create(optimizationData);
      return savedOptimization.id;
    } catch (error) {
      console.error('保存优化历史失败:', error);
      throw new Error('保存优化历史失败');
    }
  }

  /**
   * 批量优化
   */
  async batchOptimize(
    prompts: string[],
    options: Omit<OptimizationOptions, 'originalPrompt'>
  ): Promise<EnhancedOptimizationResult[]> {
    const results: EnhancedOptimizationResult[] = [];

    for (const prompt of prompts) {
      try {
        const result = await this.optimize({
          ...options,
          originalPrompt: prompt,
        });
        results.push(result);

        // 添加延迟避免 API 限制
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`批量优化失败 (${prompt.substring(0, 50)}...):`, error);
        // 继续处理其他提示词
      }
    }

    return results;
  }

  /**
   * 比较两个提示词
   */
  async comparePrompts(
    prompt1: string,
    prompt2: string,
    _profileId?: string
  ): Promise<{
    prompt1Analysis: QualityAssessment;
    prompt2Analysis: QualityAssessment;
    comparison: {
      winner: 'prompt1' | 'prompt2' | 'tie';
      scoreDifference: number;
      strengths: { prompt1: string[]; prompt2: string[] };
      recommendations: string[];
    };
  }> {
    const [analysis1, analysis2] = await Promise.all([
      this.analyzePrompt(prompt1),
      this.analyzePrompt(prompt2),
    ]);

    const scoreDifference = analysis1.score - analysis2.score;
    const winner = Math.abs(scoreDifference) < 5 ? 'tie' : 
                  scoreDifference > 0 ? 'prompt1' : 'prompt2';

    const recommendations = [];
    if (winner === 'tie') {
      recommendations.push('两个提示词质量相近，可以根据具体需求选择');
    } else {
      const betterPrompt = winner === 'prompt1' ? '第一个' : '第二个';
      recommendations.push(`${betterPrompt}提示词整体质量更高`);
    }

    return {
      prompt1Analysis: analysis1,
      prompt2Analysis: analysis2,
      comparison: {
        winner,
        scoreDifference: Math.abs(scoreDifference),
        strengths: {
          prompt1: analysis1.strengths,
          prompt2: analysis2.strengths,
        },
        recommendations,
      },
    };
  }

  /**
   * 获取优化建议
   */
  async getOptimizationSuggestions(
    prompt: string,
    _profileId?: string
  ): Promise<{
    quickFixes: string[];
    detailedSuggestions: string[];
    exampleImprovements: Array<{ before: string; after: string; reason: string }>;
  }> {
    const analysis = await this.analyzePrompt(prompt);
    
    const quickFixes = [];
    const detailedSuggestions = [];
    const exampleImprovements = [];

    // 基于分析结果生成建议
    if (analysis.factors.clarity < 70) {
      quickFixes.push('简化句子结构，使用更直接的表达');
      detailedSuggestions.push('将长句拆分为多个短句，避免复杂的从句结构');
    }

    if (analysis.factors.specificity < 70) {
      quickFixes.push('添加具体的细节和要求');
      detailedSuggestions.push('明确指定期望的输出格式、风格或具体要求');
    }

    if (analysis.factors.structure < 70) {
      quickFixes.push('使用编号或分点来组织内容');
      detailedSuggestions.push('采用清晰的结构，如：背景 → 任务 → 要求 → 输出格式');
    }

    if (analysis.factors.actionability < 70) {
      quickFixes.push('使用更明确的动作词汇');
      detailedSuggestions.push('用"请生成"、"请分析"等明确的指令替代模糊的表达');
    }

    // 生成示例改进
    if (prompt.length < 50) {
      exampleImprovements.push({
        before: '帮我写代码',
        after: '请帮我编写一个 JavaScript 函数，用于验证邮箱地址格式，包含错误处理',
        reason: '增加了具体的技术要求和功能描述',
      });
    }

    return {
      quickFixes,
      detailedSuggestions,
      exampleImprovements,
    };
  }

  /**
   * 获取优化统计信息
   */
  async getOptimizationStats(_profileId?: string): Promise<{
    totalOptimizations: number;
    averageQuality: number;
    averageImprovement: number;
    topImprovements: string[];
    recentTrends: Array<{ date: string; count: number; avgQuality: number }>;
  }> {
    try {
      // 暂时使用模拟数据，后续实现真实的统计查询
      const totalOptimizations = 42;
      const averageQuality = 78;
      const averageImprovement = 25;

      // 提取常见改进点
      const topImprovements = [
        '增加了具体的技术要求',
        '明确了输出格式',
        '改进了语言表达',
        '添加了上下文信息',
        '优化了结构组织',
      ];

      // 生成趋势数据（简化版本）
      const recentTrends = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        recentTrends.push({
          date: date.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 10) + 1,
          avgQuality: Math.floor(Math.random() * 20) + 70,
        });
      }

      return {
        totalOptimizations,
        averageQuality,
        averageImprovement,
        topImprovements,
        recentTrends,
      };
    } catch (error) {
      console.error('获取优化统计失败:', error);
      return {
        totalOptimizations: 0,
        averageQuality: 0,
        averageImprovement: 0,
        topImprovements: [],
        recentTrends: [],
      };
    }
  }
}

// 导出单例实例
export const optimizationEngine = new OptimizationEngine();
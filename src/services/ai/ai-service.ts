/**
 * AI 服务类
 * 封装与 Google Gemini API 的交互逻辑
 */

import { generateText, generateObject } from 'ai';
import { type GoogleGenerativeAIProvider, createGoogleGenerativeAI } from '@ai-sdk/google';
import { z } from 'zod';
import type { Profile } from '../../schemas';
import { aiErrorHandler } from './ai-error-handler';

// AI 配置接口
export interface AIConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// 优化请求接口
export interface OptimizationRequest {
  originalPrompt: string;
  profile?: Profile;
  context?: string;
  requirements?: string[];
}

// 优化结果接口
export interface OptimizationResult {
  optimizedPrompt: string;
  improvements: string[];
  reasoning: string;
  quality: number;
  tokensUsed: number;
  processingTime: number;
}

// 优化结果的 Zod Schema
const OptimizationResultSchema = z.object({
  optimizedPrompt: z.string().min(1, '优化后的提示词不能为空'),
  improvements: z.array(z.string()).min(1, '必须包含至少一个改进点'),
  reasoning: z.string().min(10, '推理过程说明过短'),
  quality: z.number().min(0).max(100, '质量评分必须在0-100之间'),
});

export class AIService {
  private config: AIConfig;
  private provider: GoogleGenerativeAIProvider;

  constructor(config: AIConfig) {
    this.config = config;
    if (!this.config.apiKey) {
      throw new Error('API 密钥未配置');
    }

    this.provider = createGoogleGenerativeAI({
      apiKey: this.config.apiKey,
    });
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<AIConfig>): void {
    this.config = { ...this.config, ...config };
    this.provider = createGoogleGenerativeAI({
      apiKey: this.config.apiKey,
    });
  }

  /**
   * 测试 API 连接
   */
  async testConnection(): Promise<{
    success: boolean;
    error?: string;
    latency?: number;
  }> {
    const startTime = Date.now();

    try {
      await aiErrorHandler.withRetry(async () => {
        return await generateText({
          model: this.provider(this.config.model),
          prompt: 'Hello, this is a connection test.',
          maxTokens: 10,
        });
      });

      const latency = Date.now() - startTime;

      return {
        success: true,
        latency,
      };
    } catch (error) {
      const aiError = aiErrorHandler.parseError(error);
      return {
        success: false,
        error: aiErrorHandler.getUserFriendlyMessage(aiError),
      };
    }
  }

  /**
   * 优化提示词
   */
  async optimizePrompt(request: OptimizationRequest): Promise<OptimizationResult> {
    const startTime = Date.now();

    try {
      // 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(request.profile);

      // 构建用户提示词
      const userPrompt = this.buildUserPrompt(request);

      // 调用 AI 生成优化结果
      const result = await aiErrorHandler.withRetry(async () => {
        return await generateObject({
          model: this.provider(this.config.model),
          temperature: this.config.temperature,
          maxTokens: this.config.maxTokens,
          system: systemPrompt,
          prompt: userPrompt,
          schema: OptimizationResultSchema,
        });
      });

      const processingTime = Date.now() - startTime;
      const object = result.object as z.infer<typeof OptimizationResultSchema>;

      return {
        optimizedPrompt: object.optimizedPrompt,
        improvements: object.improvements,
        reasoning: object.reasoning,
        quality: object.quality,
        tokensUsed: result.usage?.totalTokens || 0,
        processingTime,
      };
    } catch (error) {
      const aiError = aiErrorHandler.parseError(error);
      throw new Error(`提示词优化失败: ${aiErrorHandler.getUserFriendlyMessage(aiError)}`);
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(profile?: Profile): string {
    let systemPrompt = `你是一个专业的提示词优化专家，专门帮助用户改进他们的 AI 提示词。

    你的任务是：
    1. 分析用户提供的原始提示词
    2. 识别其中的问题和改进空间
    3. 提供一个优化后的版本
    4. 解释你的改进思路和推理过程
    5. 评估优化后提示词的质量（0-100分）

    优化原则：
    - 使语言更加清晰、具体和准确
    - 提供必要的上下文信息
    - 确保指令明确且易于理解
    - 优化结构和逻辑流程
    - 避免歧义和模糊表达
    - 根据使用场景调整语调和风格

    请以 JSON 格式返回结果，包含以下字段：
    - optimizedPrompt: 优化后的提示词
    - improvements: 改进点列表
    - reasoning: 详细的推理过程
    - quality: 质量评分（0-100）`;

    // 如果有个人画像，添加个性化信息
    if (profile) {
      systemPrompt += `\n\n个人画像信息：
      - 角色：${profile.role}
      - 技能：${profile.skills.join(', ')}
      - 语调风格：${profile.tone}

      请根据这个画像来调整优化策略，确保优化后的提示词符合用户的专业背景和偏好风格。`;
    }

    return systemPrompt;
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(request: OptimizationRequest): string {
    let userPrompt = `请优化以下提示词：

    原始提示词：
    """
    ${request.originalPrompt}
    """`;

    // 添加上下文信息
    if (request.context) {
      userPrompt += `\n\n使用场景：
      ${request.context}`;
    }

    // 添加特殊要求
    if (request.requirements && request.requirements.length > 0) {
      userPrompt += `\n\n特殊要求：
      ${request.requirements.map(req => `- ${req}`).join('\n')}`;
    }

    userPrompt += `\n\n请分析这个提示词的问题，并提供一个优化版本。`;

    return userPrompt;
  }

  /**
   * 批量优化提示词
   */
  async batchOptimize(requests: OptimizationRequest[]): Promise<OptimizationResult[]> {
    const results: OptimizationResult[] = [];

    // 为了避免 API 限制，我们串行处理
    for (const request of requests) {
      try {
        const result = await this.optimizePrompt(request);
        results.push(result);

        // 添加小延迟以避免触发速率限制
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('批量优化中的单个请求失败:', error);
        // 继续处理其他请求
      }
    }

    return results;
  }

  /**
   * 生成提示词建议
   */
  async generateSuggestions(topic: string, profile?: Profile): Promise<string[]> {
    try {
      const systemPrompt = `你是一个提示词创作专家，专门帮助用户生成高质量的 AI 提示词建议。

        请根据给定的主题，生成5个不同角度的提示词建议。每个建议都应该：
        - 针对不同的使用场景
        - 具有明确的目标和期望输出
        - 语言清晰、结构合理
        - 适合与 AI 模型交互

        ${profile ? `\n个人画像信息：
        - 角色：${profile.role}
        - 技能：${profile.skills.join(', ')}
        - 语调风格：${profile.tone}

        请确保建议符合用户的专业背景。` : ''}`;

      const userPrompt = `主题：${topic}
        请生成5个相关的提示词建议，以 JSON 数组格式返回。`;

      const SuggestionsSchema = z.object({
        suggestions: z.array(z.string()).length(5),
      });

      const result = await generateObject({
        model: this.provider(this.config.model),
        temperature: 0.8, // 提高创造性
        maxTokens: 1000,
        system: systemPrompt,
        prompt: userPrompt,
        schema: SuggestionsSchema,
      });

      const object = result.object as z.infer<typeof SuggestionsSchema>;
      return object.suggestions;
    } catch (error) {
      throw new Error(`生成建议失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析提示词质量
   */
  async analyzePrompt(prompt: string): Promise<{
    score: number;
    strengths: string[];
    weaknesses: string[];
    suggestions: string[];
  }> {
    try {
      const systemPrompt = `你是一个提示词质量分析专家。请分析给定的提示词，从以下维度评估：
      1. 清晰度：指令是否明确易懂
      2. 具体性：是否提供了足够的细节
      3. 结构性：逻辑结构是否合理
      4. 完整性：是否包含必要的信息
      5. 可执行性：AI 是否能够准确执行

      请提供：
      - 总体评分（0-100）
      - 优点列表
      - 缺点列表
      - 改进建议`;

      const AnalysisSchema = z.object({
        score: z.number().min(0).max(100),
        strengths: z.array(z.string()),
        weaknesses: z.array(z.string()),
        suggestions: z.array(z.string()),
      });

      const result = await generateObject({
        model: this.provider(this.config.model),
        temperature: 0.3, // 降低随机性，提高分析准确性
        maxTokens: 800,
        system: systemPrompt,
        prompt: `请分析以下提示词：\n\n"${prompt}"`,
        schema: AnalysisSchema,
      });

      return result.object as z.infer<typeof AnalysisSchema>;
    } catch (error) {
      throw new Error(`提示词分析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): Omit<AIConfig, 'apiKey'> {
    return {
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
    };
  }

  /**
   * 获取支持的模型列表
   */
  static getSupportedModels(): Array<{ id: string; name: string; description: string }> {
    return [
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: '最强大的模型，适合复杂的推理和创作任务',
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: '快速响应的模型，适合日常优化任务',
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: '平衡性能和成本的通用模型',
      },
    ];
  }

  /**
   * 验证 API 密钥格式
   */
  static validateApiKey(apiKey: string): { valid: boolean; error?: string } {
    if (!apiKey) {
      return { valid: false, error: 'API 密钥不能为空' };
    }

    if (apiKey.length < 10) {
      return { valid: false, error: 'API 密钥长度不足' };
    }

    // Google API 密钥通常以 AIza 开头
    if (!apiKey.startsWith('AIza')) {
      return { valid: false, error: 'API 密钥格式不正确，应以 AIza 开头' };
    }

    return { valid: true };
  }
}
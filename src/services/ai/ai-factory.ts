/**
 * AI 服务工厂
 * 负责创建和管理 AI 服务实例
 */

import { AIService } from './ai-service';
import { aiConfigManager } from './ai-config';
import type { AIConfig } from './ai-service';

// 私有状态
let instance: AIService | null = null;
let configListener: (() => void) | null = null;

/**
 * 获取 AI 服务实例
 */
export function getInstance(): AIService | null {
  const config = aiConfigManager.getConfig();
  
  if (!config) {
    return null;
  }

  // 如果配置发生变化，重新创建实例
  if (!instance || hasConfigChanged(config)) {
    instance = new AIService(config);
  }

  return instance;
}

/**
 * 强制重新创建实例
 */
export function recreateInstance(): AIService | null {
  instance = null;
  return getInstance();
}

/**
 * 检查配置是否发生变化
 */
function hasConfigChanged(newConfig: AIConfig): boolean {
  if (!instance) return true;
  
  const currentConfig = instance.getConfig();
  
  return (
    currentConfig.model !== newConfig.model ||
    currentConfig.temperature !== newConfig.temperature ||
    currentConfig.maxTokens !== newConfig.maxTokens
  );
}

/**
 * 初始化工厂（设置配置监听器）
 */
export function initialize(): void {
  if (configListener) return;

  configListener = aiConfigManager.addListener((config) => {
    if (config) {
      // 配置更新时重新创建实例
      instance = null;
    } else {
      // 配置被清除时销毁实例
      instance = null;
    }
  });
}

/**
 * 清理工厂
 */
export function cleanup(): void {
  if (configListener) {
    configListener();
    configListener = null;
  }
  instance = null;
}

/**
 * 检查服务是否可用
 */
export function isAvailable(): boolean {
  return aiConfigManager.isConfigured();
}

/**
 * 获取服务状态
 */
export function getStatus(): {
  available: boolean;
  configured: boolean;
  error?: string;
} {
  const configured = aiConfigManager.isConfigured();
  
  if (!configured) {
    return {
      available: false,
      configured: false,
      error: 'AI 服务未配置',
    };
  }

  try {
    const serviceInstance = getInstance();
    return {
      available: !!serviceInstance,
      configured: true,
    };
  } catch (error) {
    return {
      available: false,
      configured: true,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 初始化工厂
initialize();

// 导出便捷函数
export function getAIService(): AIService | null {
  return getInstance();
}

export function isAIServiceAvailable(): boolean {
  return isAvailable();
}

export function getAIServiceStatus() {
  return getStatus();
}
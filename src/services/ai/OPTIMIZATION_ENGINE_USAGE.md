# 优化引擎使用指南

## 概述

OptimizationEngine 是 Prompt-Snap 的核心组件，负责协调整个提示词优化流程。它集成了 AI 服务、个人画像管理、质量分析等功能。

## 基本使用

### 1. 简单优化

```typescript
import { optimizationEngine } from '@/services/ai/optimization-engine';

// 基础优化
const result = await optimizationEngine.optimize({
  originalPrompt: '帮我写代码',
  saveToHistory: true,
});

console.log('优化结果:', result.optimizedPrompt);
console.log('改进点:', result.improvements);
console.log('质量评分:', result.quality);
```

### 2. 使用个人画像优化

```typescript
// 使用特定画像优化
const result = await optimizationEngine.optimize({
  originalPrompt: '帮我写代码',
  profileId: 'profile-123',
  context: '需要编写一个用户认证系统',
  requirements: ['使用 TypeScript', '包含错误处理'],
  includeAnalysis: true,
});

console.log('使用的画像:', result.profile?.name);
console.log('原始分析:', result.analysis);
```

### 3. 生成替代方案

```typescript
// 生成多个优化方案
const result = await optimizationEngine.optimize({
  originalPrompt: '写一篇文章',
  generateAlternatives: true,
  includeAnalysis: true,
});

console.log('主要优化结果:', result.optimizedPrompt);
console.log('替代方案:');
result.alternatives?.forEach((alt, index) => {
  console.log(`${index + 1}. ${alt.focus}: ${alt.prompt} (评分: ${alt.score})`);
});
```

## 高级功能

### 1. 质量分析

```typescript
// 单独分析提示词质量
const analysis = await optimizationEngine.analyzePrompt('帮我写代码');

console.log('总体评分:', analysis.score);
console.log('各项因子:');
console.log('- 清晰度:', analysis.factors.clarity);
console.log('- 具体性:', analysis.factors.specificity);
console.log('- 结构性:', analysis.factors.structure);
console.log('- 完整性:', analysis.factors.completeness);
console.log('- 可执行性:', analysis.factors.actionability);

console.log('优点:', analysis.strengths);
console.log('缺点:', analysis.weaknesses);
console.log('建议:', analysis.suggestions);
```

### 2. 提示词对比

```typescript
// 比较两个提示词
const comparison = await optimizationEngine.comparePrompts(
  '帮我写代码',
  '请帮我编写一个 JavaScript 函数，用于验证邮箱地址格式'
);

console.log('获胜者:', comparison.comparison.winner);
console.log('分数差异:', comparison.comparison.scoreDifference);
console.log('建议:', comparison.comparison.recommendations);
```

### 3. 批量优化

```typescript
// 批量处理多个提示词
const prompts = [
  '帮我写代码',
  '写一篇文章',
  '分析数据',
];

const results = await optimizationEngine.batchOptimize(prompts, {
  profileId: 'profile-123',
  saveToHistory: true,
});

results.forEach((result, index) => {
  console.log(`提示词 ${index + 1} 优化结果:`, result.optimizedPrompt);
});
```

### 4. 获取优化建议

```typescript
// 获取针对性的优化建议
const suggestions = await optimizationEngine.getOptimizationSuggestions(
  '帮我写代码'
);

console.log('快速修复建议:', suggestions.quickFixes);
console.log('详细建议:', suggestions.detailedSuggestions);
console.log('示例改进:', suggestions.exampleImprovements);
```

### 5. 统计信息

```typescript
// 获取优化统计数据
const stats = await optimizationEngine.getOptimizationStats();

console.log('总优化次数:', stats.totalOptimizations);
console.log('平均质量:', stats.averageQuality);
console.log('平均改进幅度:', stats.averageImprovement);
console.log('常见改进点:', stats.topImprovements);
console.log('最近趋势:', stats.recentTrends);
```

## 配置选项

### OptimizationOptions

```typescript
interface OptimizationOptions {
  // 必需参数
  originalPrompt: string;           // 原始提示词
  
  // 可选参数
  profileId?: string;               // 个人画像 ID
  context?: string;                 // 使用场景描述
  requirements?: string[];          // 特殊要求列表
  
  // 控制选项
  saveToHistory?: boolean;          // 是否保存到历史记录（默认 true）
  generateAlternatives?: boolean;   // 是否生成替代方案（默认 false）
  includeAnalysis?: boolean;        // 是否包含质量分析（默认 false）
}
```

### EnhancedOptimizationResult

```typescript
interface EnhancedOptimizationResult {
  // 基础优化结果
  optimizedPrompt: string;          // 优化后的提示词
  improvements: string[];           // 改进点列表
  reasoning: string;                // 优化推理过程
  quality: number;                  // 质量评分 (0-100)
  tokensUsed: number;              // 使用的 token 数量
  processingTime: number;          // 处理时间 (ms)
  
  // 扩展信息
  originalPrompt: string;          // 原始提示词
  profile?: Profile;               // 使用的个人画像
  analysis?: {                     // 原始提示词分析
    originalScore: number;
    originalStrengths: string[];
    originalWeaknesses: string[];
    improvementAreas: string[];
  };
  alternatives?: Array<{           // 替代方案
    prompt: string;
    focus: string;
    score: number;
  }>;
  historyId?: string;             // 历史记录 ID
}
```

## 错误处理

```typescript
try {
  const result = await optimizationEngine.optimize({
    originalPrompt: '帮我写代码',
  });
} catch (error) {
  if (error.message.includes('AI 服务未配置')) {
    console.error('请先配置 AI API 密钥');
  } else if (error.message.includes('个人画像不存在')) {
    console.error('指定的个人画像无效');
  } else {
    console.error('优化失败:', error.message);
  }
}
```

## 最佳实践

1. **合理使用分析功能**: `includeAnalysis` 会增加 API 调用，仅在需要详细分析时使用
2. **批量处理**: 对于多个提示词，使用 `batchOptimize` 而不是多次调用 `optimize`
3. **历史记录**: 重要的优化结果建议保存到历史记录，便于后续查看和比较
4. **错误处理**: 始终包含适当的错误处理，特别是网络和 API 相关的错误
5. **性能考虑**: 替代方案生成会产生多次 API 调用，谨慎使用

## 集成示例

### 在 React 组件中使用

```typescript
import { useState } from 'react';
import { optimizationEngine } from '@/services/ai/optimization-engine';

function OptimizeForm() {
  const [prompt, setPrompt] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleOptimize = async () => {
    setLoading(true);
    try {
      const optimizationResult = await optimizationEngine.optimize({
        originalPrompt: prompt,
        includeAnalysis: true,
      });
      setResult(optimizationResult);
    } catch (error) {
      console.error('优化失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <textarea 
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder="输入要优化的提示词..."
      />
      <button onClick={handleOptimize} disabled={loading}>
        {loading ? '优化中...' : '开始优化'}
      </button>
      {result && (
        <div>
          <h3>优化结果:</h3>
          <p>{result.optimizedPrompt}</p>
          <h4>改进点:</h4>
          <ul></ul>  {result.improvements.map((improvement, index) => (
              <li key={index}>{improvement}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
```
/**
 * AI 服务统一入口
 * 导出所有 AI 相关的服务和工具
 */

import { getAIService } from "./ai-factory";

// 核心服务
export { AIService } from "./ai-service";
export {
  getInstance,
  recreateInstance,
  initialize,
  cleanup,
  isAvailable,
  getStatus,
  getAIService,
  isAIServiceAvailable,
  getAIServiceStatus,
} from "./ai-factory";
export { aiConfigManager, AIConfigManager } from "./ai-config";

// 优化引擎
export { OptimizationEngine, optimizationEngine } from "./optimization-engine";
export type {
  OptimizationOptions,
  EnhancedOptimizationResult,
  QualityAssessment,
} from "./optimization-engine";

// 错误处理
export {
  aiErrorHandler,
  AIErrorHandler,
  AIError,
  AIErrorType,
} from "./ai-error-handler";

// 类型定义
export type {
  AIConfig,
  OptimizationRequest,
  OptimizationResult,
} from "./ai-service";

export type { RetryConfig } from "./ai-error-handler";

// 便捷函数 - 使用优化引擎
import { optimizationEngine } from "./optimization-engine";

export async function optimizePrompt(
  originalPrompt: string,
  options?: {
    profileId?: string;
    context?: string;
    requirements?: string[];
    saveToHistory?: boolean;
    generateAlternatives?: boolean;
    includeAnalysis?: boolean;
  }
) {
  return optimizationEngine.optimize({
    originalPrompt,
    ...options,
  });
}

export async function optimizePromptBasic(
  originalPrompt: string,
  profile?: any,
  context?: string,
  requirements?: string[]
) {
  const aiService = getAIService();

  if (!aiService) {
    throw new Error("AI 服务未配置，请先在设置中配置 API 密钥");
  }

  return aiService.optimizePrompt({
    originalPrompt,
    profile,
    context,
    requirements,
  });
}

export async function generateSuggestions(topic: string, profile?: any) {
  const aiService = getAIService();

  if (!aiService) {
    throw new Error("AI 服务未配置，请先在设置中配置 API 密钥");
  }

  return aiService.generateSuggestions(topic, profile);
}

export async function analyzePrompt(prompt: string) {
  return optimizationEngine.analyzePrompt(prompt);
}

export async function analyzePromptBasic(prompt: string) {
  const aiService = getAIService();

  if (!aiService) {
    throw new Error("AI 服务未配置，请先在设置中配置 API 密钥");
  }

  return aiService.analyzePrompt(prompt);
}

// 新增的优化引擎便捷函数
export async function comparePrompts(prompt1: string, prompt2: string) {
  return optimizationEngine.comparePrompts(prompt1, prompt2);
}

export async function getOptimizationSuggestions(prompt: string) {
  return optimizationEngine.getOptimizationSuggestions(prompt);
}

export async function batchOptimize(
  prompts: string[],
  options?: {
    profileId?: string;
    context?: string;
    requirements?: string[];
    saveToHistory?: boolean;
  }
) {
  return optimizationEngine.batchOptimize(prompts, options || {});
}

export async function getOptimizationStats(profileId?: string) {
  return optimizationEngine.getOptimizationStats(profileId);
}

export async function testAIConnection() {
  const aiService = getAIService();

  if (!aiService) {
    return {
      success: false,
      error: "AI 服务未配置",
    };
  }

  return aiService.testConnection();
}

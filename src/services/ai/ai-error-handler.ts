/**
 * AI 服务错误处理器
 * 处理 AI API 调用中的各种错误情况
 */

// AI 错误类型
export enum AIErrorType {
  API_KEY_INVALID = 'API_KEY_INVALID',
  API_KEY_MISSING = 'API_KEY_MISSING',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  RATE_LIMITED = 'RATE_LIMITED',
  MODEL_NOT_FOUND = 'MODEL_NOT_FOUND',
  CONTENT_FILTERED = 'CONTENT_FILTERED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN',
}

// AI 错误类
export class AIError extends Error {
  public readonly type: AIErrorType;
  public readonly originalError?: Error;
  public readonly retryable: boolean;
  public readonly retryAfter?: number; // 重试延迟（毫秒）

  constructor(
    type: AIErrorType,
    message: string,
    originalError?: Error,
    retryable: boolean = false,
    retryAfter?: number
  ) {
    super(message);
    this.name = 'AIError';
    this.type = type;
    this.originalError = originalError;
    this.retryable = retryable;
    this.retryAfter = retryAfter;
  }
}

// 重试配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // 基础延迟（毫秒）
  maxDelay: number; // 最大延迟（毫秒）
  backoffMultiplier: number; // 退避倍数
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
};

export class AIErrorHandler {
  private retryConfig: RetryConfig;

  constructor(retryConfig: Partial<RetryConfig> = {}) {
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
  }

  /**
   * 解析错误并转换为 AIError
   */
  parseError(error: unknown): AIError {
    if (error instanceof AIError) {
      return error;
    }

    if (error instanceof Error) {
      return this.classifyError(error);
    }

    return new AIError(
      AIErrorType.UNKNOWN,
      typeof error === 'string' ? error : '未知错误',
      undefined,
      false
    );
  }

  /**
   * 分类错误类型
   */
  private classifyError(error: Error): AIError {
    const message = error.message.toLowerCase();

    // API 密钥相关错误
    if (message.includes('api key') || message.includes('unauthorized') || message.includes('401')) {
      if (message.includes('invalid') || message.includes('incorrect')) {
        return new AIError(
          AIErrorType.API_KEY_INVALID,
          'API 密钥无效，请检查密钥是否正确',
          error,
          false
        );
      }
      return new AIError(
        AIErrorType.API_KEY_MISSING,
        'API 密钥缺失，请配置有效的 API 密钥',
        error,
        false
      );
    }

    // 配额超限
    if (message.includes('quota') || message.includes('limit') || message.includes('429')) {
      const retryAfter = this.extractRetryAfter(error.message);
      return new AIError(
        AIErrorType.QUOTA_EXCEEDED,
        'API 配额已用完或请求频率过高，请稍后重试',
        error,
        true,
        retryAfter
      );
    }

    // 速率限制
    if (message.includes('rate limit') || message.includes('too many requests')) {
      const retryAfter = this.extractRetryAfter(error.message);
      return new AIError(
        AIErrorType.RATE_LIMITED,
        '请求频率过高，请稍后重试',
        error,
        true,
        retryAfter || 5000
      );
    }

    // 模型不存在
    if (message.includes('model') && (message.includes('not found') || message.includes('404'))) {
      return new AIError(
        AIErrorType.MODEL_NOT_FOUND,
        '指定的 AI 模型不存在或不可用',
        error,
        false
      );
    }

    // 内容过滤
    if (message.includes('content') && message.includes('filter')) {
      return new AIError(
        AIErrorType.CONTENT_FILTERED,
        '内容被安全过滤器拦截，请修改输入内容',
        error,
        false
      );
    }

    // 网络错误
    if (message.includes('network') || message.includes('connection') || message.includes('fetch')) {
      return new AIError(
        AIErrorType.NETWORK_ERROR,
        '网络连接错误，请检查网络连接',
        error,
        true,
        2000
      );
    }

    // 超时错误
    if (message.includes('timeout') || message.includes('aborted')) {
      return new AIError(
        AIErrorType.TIMEOUT,
        '请求超时，请稍后重试',
        error,
        true,
        1000
      );
    }

    // 默认为未知错误
    return new AIError(
      AIErrorType.UNKNOWN,
      `AI 服务错误: ${error.message}`,
      error,
      true
    );
  }

  /**
   * 从错误消息中提取重试延迟时间
   */
  private extractRetryAfter(message: string): number | undefined {
    const match = message.match(/retry[- ]?after[:\s]*(\d+)/i);
    if (match) {
      return parseInt(match[1]) * 1000; // 转换为毫秒
    }
    return undefined;
  }

  /**
   * 带重试的异步函数执行
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    customRetryConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.retryConfig, ...customRetryConfig };
    let lastError: AIError;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.parseError(error);

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === config.maxRetries) {
          throw lastError;
        }

        // 如果错误不可重试，直接抛出
        if (!lastError.retryable) {
          throw lastError;
        }

        // 计算延迟时间
        const delay = this.calculateDelay(attempt, config, lastError.retryAfter);
        
        console.warn(`AI 操作失败，${delay}ms 后重试 (${attempt + 1}/${config.maxRetries}):`, lastError.message);
        
        // 等待后重试
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * 计算重试延迟时间
   */
  private calculateDelay(attempt: number, config: RetryConfig, retryAfter?: number): number {
    // 如果服务器指定了重试时间，优先使用
    if (retryAfter) {
      return Math.min(retryAfter, config.maxDelay);
    }

    // 指数退避算法
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.1 * delay;
    
    return Math.min(delay + jitter, config.maxDelay);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取用户友好的错误消息
   */
  getUserFriendlyMessage(error: AIError): string {
    switch (error.type) {
      case AIErrorType.API_KEY_INVALID:
        return '您的 API 密钥无效，请在设置中检查并更新密钥。';
      
      case AIErrorType.API_KEY_MISSING:
        return '请先在设置中配置 Google AI API 密钥。';
      
      case AIErrorType.QUOTA_EXCEEDED:
        return '您的 API 配额已用完，请稍后重试或升级您的账户。';
      
      case AIErrorType.RATE_LIMITED:
        return '请求过于频繁，请稍后重试。';
      
      case AIErrorType.MODEL_NOT_FOUND:
        return '所选的 AI 模型不可用，请在设置中选择其他模型。';
      
      case AIErrorType.CONTENT_FILTERED:
        return '您的内容被安全过滤器拦截，请修改后重试。';
      
      case AIErrorType.NETWORK_ERROR:
        return '网络连接失败，请检查您的网络连接。';
      
      case AIErrorType.TIMEOUT:
        return '请求超时，请稍后重试。';
      
      default:
        return '服务暂时不可用，请稍后重试。';
    }
  }

  /**
   * 获取错误的建议解决方案
   */
  getSuggestions(error: AIError): string[] {
    switch (error.type) {
      case AIErrorType.API_KEY_INVALID:
        return [
          '检查 API 密钥是否正确复制',
          '确认密钥没有过期',
          '重新生成新的 API 密钥',
        ];
      
      case AIErrorType.API_KEY_MISSING:
        return [
          '访问 Google AI Studio 获取 API 密钥',
          '在应用设置中配置 API 密钥',
        ];
      
      case AIErrorType.QUOTA_EXCEEDED:
        return [
          '等待配额重置（通常每月重置）',
          '升级到付费计划',
          '优化提示词以减少 Token 使用',
        ];
      
      case AIErrorType.RATE_LIMITED:
        return [
          '降低请求频率',
          '等待几分钟后重试',
          '考虑批量处理请求',
        ];
      
      case AIErrorType.NETWORK_ERROR:
        return [
          '检查网络连接',
          '尝试切换网络',
          '检查防火墙设置',
        ];
      
      default:
        return [
          '稍后重试',
          '检查网络连接',
          '联系技术支持',
        ];
    }
  }

  /**
   * 更新重试配置
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
  }

  /**
   * 获取当前重试配置
   */
  getRetryConfig(): RetryConfig {
    return { ...this.retryConfig };
  }
}

// 导出默认实例
export const aiErrorHandler = new AIErrorHandler();
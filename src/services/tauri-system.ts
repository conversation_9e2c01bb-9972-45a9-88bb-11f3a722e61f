/**
 * Tauri 系统集成服务
 * 封装全局快捷键、剪贴板和数据库操作
 */

import { register, unregister } from '@tauri-apps/plugin-global-shortcut';
import { readText, writeText } from '@tauri-apps/plugin-clipboard-manager';
import Database from '@tauri-apps/plugin-sql';

export class TauriSystemService {
  private db: Database | null = null;

  /**
   * 初始化数据库连接
   */
  async initializeDatabase(): Promise<void> {
    try {
      this.db = await Database.load('sqlite:prompt_snap.db');
      console.log('数据库连接成功');
    } catch (error) {
      console.error('数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): Database | null {
    return this.db;
  }

  /**
   * 注册全局快捷键
   */
  async registerGlobalShortcut(shortcut: string, callback: () => void): Promise<void> {
    try {
      await register(shortcut, callback);
      console.log(`全局快捷键 ${shortcut} 注册成功`);
    } catch (error) {
      console.error(`注册全局快捷键 ${shortcut} 失败:`, error);
      throw error;
    }
  }

  /**
   * 测试快捷键是否可用
   */
  async testShortcutAvailability(shortcut: string): Promise<boolean> {
    try {
      await this.registerGlobalShortcut(shortcut, () => {});
      await this.unregisterGlobalShortcut(shortcut);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 注销全局快捷键
   */
  async unregisterGlobalShortcut(shortcut: string): Promise<void> {
    try {
      await unregister(shortcut);
      console.log(`全局快捷键 ${shortcut} 注销成功`);
    } catch (error) {
      console.error('注销全局快捷键失败:', error);
      throw error;
    }
  }

  /**
   * 读取剪贴板内容
   */
  async getClipboardText(): Promise<string | null> {
    try {
      const text = await readText();
      return text || null;
    } catch (error) {
      console.error('读取剪贴板失败:', error);
      return null;
    }
  }

  /**
   * 写入剪贴板内容
   */
  async setClipboardText(text: string): Promise<void> {
    try {
      await writeText(text);
      console.log('剪贴板写入成功');
    } catch (error) {
      console.error('写入剪贴板失败:', error);
      throw error;
    }
  }

  /**
   * 测试所有插件功能
   */
  async testPlugins(): Promise<void> {
    console.log('开始测试 Tauri 插件...');
    const results: string[] = [];

    // 测试数据库
    try {
      await this.initializeDatabase();
      console.log('✅ 数据库插件正常');
      results.push('✅ 数据库插件');
    } catch (error) {
      console.error('❌ 数据库插件异常:', error);
      results.push('❌ 数据库插件');
    }

    // 测试剪贴板
    try {
      const clipboardText = await this.getClipboardText();
      console.log('✅ 剪贴板插件正常，当前内容:', clipboardText?.substring(0, 50) || '(空)');
      results.push('✅ 剪贴板插件');
    } catch (error) {
      console.error('❌ 剪贴板插件异常:', error);
      results.push('❌ 剪贴板插件');
    }

    // 测试全局快捷键（注册后立即注销）
    try {
      const testShortcut = 'CommandOrControl+Alt+Shift+P';
      await this.registerGlobalShortcut(testShortcut, () => {
        console.log('测试快捷键触发');
      });
      await this.unregisterGlobalShortcut(testShortcut);
      console.log('✅ 全局快捷键插件正常');
      results.push('✅ 全局快捷键插件');
    } catch (error) {
      console.error('❌ 全局快捷键插件异常:', error);
      // 快捷键冲突不应该阻止测试继续
      console.log('⚠️ 全局快捷键可能与系统快捷键冲突，但插件功能正常');
      results.push('⚠️ 全局快捷键插件 (可能有快捷键冲突)');
    }

    console.log('Tauri 插件测试完成');
    console.log('测试结果汇总:', results.join(', '));
  }
}

// 导出单例实例
export const tauriSystem = new TauriSystemService();
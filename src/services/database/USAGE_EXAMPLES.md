# 数据库初始化使用示例

## 基本使用

### 1. 应用启动时的数据库初始化

```typescript
import { initializeDatabase } from '@/services/database';

// 应用启动时调用
async function startApp() {
  try {
    const result = await initializeDatabase({
      autoRepair: true,
      resetInDevelopment: process.env.NODE_ENV === 'development',
      timeout: 30000,
      onProgress: (step, progress) => {
        console.log(`${step} - ${progress}%`);
      },
      onError: (error) => {
        console.error('数据库初始化错误:', error);
      },
    });

    if (result.success) {
      console.log('数据库初始化成功');
      console.log(`版本: ${result.details?.version}`);
      console.log(`迁移数: ${result.details?.migrationsApplied}`);
      console.log(`种子数据: ${result.details?.seedDataInserted ? '已插入' : '跳过'}`);
      
      if (result.repaired) {
        console.log('数据库已修复:', result.details?.repairActions);
      }
    } else {
      console.error('数据库初始化失败:', result.error);
      // 处理初始化失败的情况
    }
  } catch (error) {
    console.error('应用启动失败:', error);
  }
}
```

### 2. 使用 DataAccessLayer 的完整初始化

```typescript
import { dal } from '@/services/database';

async function initializeWithMigrations() {
  try {
    await dal.initializeWithMigrations({
      runMigrations: true,
      insertSeedData: true,
      validateDatabase: true,
      resetInDevelopment: false,
      timeout: 30000,
    });

    console.log('数据库初始化完成，可以开始使用');
    
    // 现在可以使用数据库操作
    const profiles = await dal.profiles.getAll();
    console.log(`找到 ${profiles.total} 个画像`);
  } catch (error) {
    console.error('数据库初始化失败:', error);
  }
}
```

## 高级使用

### 3. 手动控制初始化流程

```typescript
import { dal } from '@/services/database';

async function manualInitialization() {
  try {
    // 1. 检查是否需要初始化
    const needs = await dal.initializer.needsInitialization();
    console.log('需要迁移:', needs.needsMigration);
    console.log('需要种子数据:', needs.needsSeedData);
    console.log('当前版本:', needs.currentVersion);

    // 2. 获取详细状态
    const status = await dal.initializer.getStatus();
    console.log('数据库状态:', status);

    // 3. 根据需要执行初始化
    if (needs.needsMigration || needs.needsSeedData) {
      const result = await dal.initializer.initialize({
        runMigrations: needs.needsMigration,
        insertSeedData: needs.needsSeedData,
        validateDatabase: true,
      });

      if (result.success) {
        console.log('初始化完成');
      } else {
        console.error('初始化失败:', result.error);
      }
    }
  } catch (error) {
    console.error('手动初始化失败:', error);
  }
}
```

### 4. 数据库健康检查和修复

```typescript
import { checkDatabaseHealth, repairDatabase } from '@/services/database';

async function healthCheckAndRepair() {
  try {
    // 检查数据库健康状态
    const health = await checkDatabaseHealth();
    
    if (health.healthy) {
      console.log('数据库状态良好');
    } else {
      console.log('发现问题:', health.issues);
      console.log('建议操作:', health.recommendations);

      // 尝试自动修复
      const repairResult = await repairDatabase();
      
      if (repairResult.success) {
        console.log('修复成功:', repairResult.actions);
      } else {
        console.error('修复失败:', repairResult.error);
      }
    }
  } catch (error) {
    console.error('健康检查失败:', error);
  }
}
```

### 5. 数据库备份和恢复

```typescript
import { dal } from '@/services/database';

async function backupAndRestore() {
  try {
    // 创建备份
    const backupResult = await dal.initializer.createBackup();
    
    if (backupResult.success) {
      console.log('备份创建成功:', backupResult.backupPath);
      
      // 从备份恢复（示例）
      const restoreResult = await dal.initializer.restoreFromBackup(backupResult.backupPath!);
      
      if (restoreResult.success) {
        console.log('恢复成功');
      } else {
        console.error('恢复失败:', restoreResult.error);
      }
    } else {
      console.error('备份失败:', backupResult.error);
    }
  } catch (error) {
    console.error('备份恢复操作失败:', error);
  }
}
```

## React 应用中的使用

### 6. 在 React 应用中使用

```typescript
// App.tsx
import React, { useEffect, useState } from 'react';
import { initializeDatabase } from '@/services/database';

interface DatabaseStatus {
  initialized: boolean;
  loading: boolean;
  error: string | null;
  progress: number;
}

function App() {
  const [dbStatus, setDbStatus] = useState<DatabaseStatus>({
    initialized: false,
    loading: true,
    error: null,
    progress: 0,
  });

  useEffect(() => {
    async function initDB() {
      try {
        const result = await initializeDatabase({
          autoRepair: true,
          onProgress: (step, progress) => {
            setDbStatus(prev => ({ ...prev, progress }));
          },
          onError: (error) => {
            setDbStatus(prev => ({ ...prev, error: error.message }));
          },
        });

        if (result.success) {
          setDbStatus({
            initialized: true,
            loading: false,
            error: null,
            progress: 100,
          });
        } else {
          setDbStatus({
            initialized: false,
            loading: false,
            error: result.error || '未知错误',
            progress: 0,
          });
        }
      } catch (error) {
        setDbStatus({
          initialized: false,
          loading: false,
          error: error instanceof Error ? error.message : '初始化失败',
          progress: 0,
        });
      }
    }

    initDB();
  }, []);

  if (dbStatus.loading) {
    return (
      <div className="loading-screen">
        <h2>正在初始化数据库...</h2>
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${dbStatus.progress}%` }}
          />
        </div>
        <p>{dbStatus.progress}%</p>
      </div>
    );
  }

  if (dbStatus.error) {
    return (
      <div className="error-screen">
        <h2>数据库初始化失败</h2>
        <p>{dbStatus.error}</p>
        <button onClick={() => window.location.reload()}>
          重试
        </button>
      </div>
    );
  }

  if (!dbStatus.initialized) {
    return (
      <div className="error-screen">
        <h2>数据库未初始化</h2>
        <button onClick={() => window.location.reload()}>
          重新加载
        </button>
      </div>
    );
  }

  // 数据库初始化成功，渲染主应用
  return (
    <div className="app">
      <h1>Prompt Snap</h1>
      {/* 主应用内容 */}
    </div>
  );
}

export default App;
```

### 7. 自定义 Hook 用于数据库状态管理

```typescript
// hooks/useDatabase.ts
import { useState, useEffect } from 'react';
import { checkDatabaseHealth, repairDatabase } from '@/services/database';

interface DatabaseHealth {
  healthy: boolean;
  issues: string[];
  recommendations: string[];
  loading: boolean;
  error: string | null;
}

export function useDatabaseHealth() {
  const [health, setHealth] = useState<DatabaseHealth>({
    healthy: false,
    issues: [],
    recommendations: [],
    loading: true,
    error: null,
  });

  const checkHealth = async () => {
    try {
      setHealth(prev => ({ ...prev, loading: true, error: null }));
      
      const result = await checkDatabaseHealth();
      
      setHealth({
        healthy: result.healthy,
        issues: result.issues,
        recommendations: result.recommendations,
        loading: false,
        error: null,
      });
    } catch (error) {
      setHealth(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '检查失败',
      }));
    }
  };

  const repair = async () => {
    try {
      const result = await repairDatabase();
      
      if (result.success) {
        // 修复成功后重新检查健康状态
        await checkHealth();
        return { success: true, actions: result.actions };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '修复失败',
      };
    }
  };

  useEffect(() => {
    checkHealth();
  }, []);

  return {
    health,
    checkHealth,
    repair,
  };
}
```

## 错误处理

### 8. 完整的错误处理示例

```typescript
import { dal, DatabaseStartupService } from '@/services/database';

async function robustInitialization() {
  const startup = new DatabaseStartupService({
    autoRepair: true,
    timeout: 60000,
    onError: (error) => {
      console.error('启动错误:', error);
      // 可以在这里发送错误报告
    },
    onProgress: (step, progress) => {
      console.log(`${step}: ${progress}%`);
    },
  });

  try {
    const result = await startup.startup();

    if (result.success) {
      console.log('数据库启动成功');
      
      // 定期健康检查
      setInterval(async () => {
        const health = await startup.healthCheck();
        if (!health.healthy) {
          console.warn('数据库健康检查失败:', health.issues);
          
          // 尝试自动修复
          const repairResult = await startup.repair();
          if (repairResult.success) {
            console.log('自动修复成功:', repairResult.actions);
          }
        }
      }, 5 * 60 * 1000); // 每5分钟检查一次

    } else {
      console.error('数据库启动失败:', result.error);
      
      // 根据错误类型采取不同的处理策略
      if (result.error?.includes('超时')) {
        console.log('尝试增加超时时间重试...');
        // 重试逻辑
      } else if (result.error?.includes('权限')) {
        console.log('检查文件权限...');
        // 权限处理逻辑
      } else {
        console.log('尝试重置数据库...');
        // 重置逻辑（仅开发环境）
      }
    }
  } catch (error) {
    console.error('启动过程中发生未预期的错误:', error);
    
    // 最后的错误处理
    if (error instanceof Error) {
      if (error.message.includes('SQLITE_CANTOPEN')) {
        console.error('无法打开数据库文件，检查权限和路径');
      } else if (error.message.includes('SQLITE_CORRUPT')) {
        console.error('数据库文件损坏，需要恢复或重建');
      } else {
        console.error('其他数据库错误:', error.message);
      }
    }
  }
}
```

## 开发和调试

### 9. 开发环境的特殊处理

```typescript
// 开发环境的数据库初始化
async function developmentInit() {
  if (process.env.NODE_ENV === 'development') {
    const result = await initializeDatabase({
      resetInDevelopment: true, // 开发环境重置数据库
      autoRepair: true,
      timeout: 10000, // 开发环境较短的超时时间
      onProgress: (step, progress) => {
        console.log(`[DEV] ${step}: ${progress}%`);
      },
    });

    if (result.success) {
      console.log('[DEV] 开发数据库初始化完成');
      
      // 开发环境可以插入测试数据
      await insertTestData();
    }
  } else {
    // 生产环境的初始化
    await initializeDatabase({
      autoRepair: true,
      resetInDevelopment: false,
      timeout: 30000,
    });
  }
}

async function insertTestData() {
  // 插入一些测试数据用于开发
  try {
    await dal.prompts.create({
      title: '测试提示词',
      content: '这是一个测试提示词',
      tags: ['测试', '开发'],
      isFavorite: false,
    });
    
    console.log('[DEV] 测试数据插入完成');
  } catch (error) {
    console.warn('[DEV] 测试数据插入失败:', error);
  }
}
```

这些示例展示了数据库初始化系统的各种使用方式，从简单的应用启动到复杂的错误处理和恢复策略。
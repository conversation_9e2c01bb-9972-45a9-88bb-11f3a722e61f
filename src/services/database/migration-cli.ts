/**
 * 数据库迁移 CLI 工具
 * 用于开发和维护阶段的迁移管理
 */

import { DatabaseMigration } from './migration';
import { DataAccessLayer } from './index';

export interface MigrationCommand {
  command: string;
  description: string;
  handler: (args: string[]) => Promise<void>;
}

export class MigrationCLI {
  private migration: DatabaseMigration;
  private dal: DataAccessLayer;
  private commands: Map<string, MigrationCommand>;

  constructor() {
    this.migration = new DatabaseMigration();
    this.dal = new DataAccessLayer();
    this.commands = new Map();
    this.registerCommands();
  }

  /**
   * 注册所有命令
   */
  private registerCommands(): void {
    this.commands.set('status', {
      command: 'status',
      description: '显示数据库迁移状态',
      handler: this.handleStatus.bind(this),
    });

    this.commands.set('migrate', {
      command: 'migrate',
      description: '执行所有待应用的迁移',
      handler: this.handleMigrate.bind(this),
    });

    this.commands.set('rollback', {
      command: 'rollback',
      description: '回滚到指定版本 (rollback <version>)',
      handler: this.handleRollback.bind(this),
    });

    this.commands.set('reset', {
      command: 'reset',
      description: '重置数据库（危险操作）',
      handler: this.handleReset.bind(this),
    });

    this.commands.set('validate', {
      command: 'validate',
      description: '验证数据库完整性',
      handler: this.handleValidate.bind(this),
    });

    this.commands.set('create', {
      command: 'create',
      description: '创建新的迁移文件 (create <name>)',
      handler: this.handleCreate.bind(this),
    });

    this.commands.set('help', {
      command: 'help',
      description: '显示帮助信息',
      handler: this.handleHelp.bind(this),
    });
  }

  /**
   * 执行命令
   */
  async execute(command: string, args: string[] = []): Promise<void> {
    const cmd = this.commands.get(command);
    
    if (!cmd) {
      console.error(`未知命令: ${command}`);
      await this.handleHelp([]);
      return;
    }

    try {
      await this.dal.initialize(false);
      await cmd.handler(args);
    } catch (error) {
      console.error(`执行命令 ${command} 失败:`, error);
      process.exit(1);
    }
  }

  /**
   * 处理 status 命令
   */
  private async handleStatus(args: string[]): Promise<void> {
    console.log('=== 数据库迁移状态 ===\n');

    const status = await this.migration.getDatabaseStatus();
    
    console.log(`当前版本: ${status.currentVersion}`);
    console.log(`最新版本: ${status.latestVersion}`);
    console.log(`状态: ${status.isUpToDate ? '最新' : '需要迁移'}\n`);

    if (status.appliedMigrations.length > 0) {
      console.log('已应用的迁移:');
      for (const migration of status.appliedMigrations) {
        console.log(`  ${migration.version}: ${migration.name} (${migration.appliedAt.toISOString()})`);
      }
      console.log();
    }

    if (status.pendingMigrations.length > 0) {
      console.log('待应用的迁移:');
      for (const migration of status.pendingMigrations) {
        console.log(`  ${migration.version}: ${migration.name} - ${migration.description}`);
      }
      console.log();
    }
  }

  /**
   * 处理 migrate 命令
   */
  private async handleMigrate(args: string[]): Promise<void> {
    console.log('=== 执行数据库迁移 ===\n');

    const beforeVersion = await this.migration.getCurrentVersion();
    
    await this.migration.migrate();
    
    const afterVersion = await this.migration.getCurrentVersion();
    const migrationsApplied = afterVersion - beforeVersion;

    if (migrationsApplied > 0) {
      console.log(`\n✅ 成功应用 ${migrationsApplied} 个迁移`);
      console.log(`数据库版本: ${beforeVersion} → ${afterVersion}`);
    } else {
      console.log('✅ 数据库已是最新版本');
    }
  }

  /**
   * 处理 rollback 命令
   */
  private async handleRollback(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.error('请指定回滚的目标版本: rollback <version>');
      return;
    }

    const targetVersion = parseInt(args[0]);
    if (isNaN(targetVersion)) {
      console.error('版本号必须是数字');
      return;
    }

    console.log(`=== 回滚到版本 ${targetVersion} ===\n`);

    const currentVersion = await this.migration.getCurrentVersion();
    
    if (targetVersion >= currentVersion) {
      console.error('目标版本必须小于当前版本');
      return;
    }

    // 确认操作
    console.log(`⚠️  警告: 即将从版本 ${currentVersion} 回滚到版本 ${targetVersion}`);
    console.log('这可能会导致数据丢失！');
    
    // 在实际应用中，这里应该有用户确认机制
    if (process.env.NODE_ENV === 'production') {
      console.error('生产环境不允许回滚操作');
      return;
    }

    await this.migration.rollbackTo(targetVersion);
    
    console.log(`✅ 成功回滚到版本 ${targetVersion}`);
  }

  /**
   * 处理 reset 命令
   */
  private async handleReset(args: string[]): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      console.error('生产环境不允许重置数据库');
      return;
    }

    console.log('=== 重置数据库 ===\n');
    console.log('⚠️  警告: 即将删除所有数据！');
    
    // 在实际应用中，这里应该有用户确认机制
    await this.migration.resetDatabase();
    
    console.log('✅ 数据库重置完成');
  }

  /**
   * 处理 validate 命令
   */
  private async handleValidate(args: string[]): Promise<void> {
    console.log('=== 验证数据库完整性 ===\n');

    const validation = await this.migration.validateDatabase();
    
    if (validation.isValid) {
      console.log('✅ 数据库验证通过');
    } else {
      console.log('❌ 数据库验证失败');
      
      if (validation.errors.length > 0) {
        console.log('\n错误:');
        for (const error of validation.errors) {
          console.log(`  - ${error}`);
        }
      }
    }

    if (validation.warnings.length > 0) {
      console.log('\n警告:');
      for (const warning of validation.warnings) {
        console.log(`  - ${warning}`);
      }
    }
  }

  /**
   * 处理 create 命令
   */
  private async handleCreate(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.error('请指定迁移名称: create <name>');
      return;
    }

    const name = args[0];
    const currentVersion = await this.migration.getCurrentVersion();
    const newVersion = currentVersion + 1;
    
    const filename = `${String(newVersion).padStart(3, '0')}_${name}.sql`;
    
    console.log(`=== 创建新迁移 ===\n`);
    console.log(`版本: ${newVersion}`);
    console.log(`名称: ${name}`);
    console.log(`文件: ${filename}\n`);

    const template = this.generateMigrationTemplate(newVersion, name);
    
    console.log('迁移模板:');
    console.log('```sql');
    console.log(template);
    console.log('```');
    
    console.log(`\n请将上述内容保存到 database/migrations/${filename}`);
  }

  /**
   * 处理 help 命令
   */
  private async handleHelp(args: string[]): Promise<void> {
    console.log('=== 数据库迁移 CLI 工具 ===\n');
    console.log('用法: migration-cli <command> [args]\n');
    console.log('可用命令:');
    
    for (const [name, cmd] of this.commands) {
      console.log(`  ${cmd.command.padEnd(12)} ${cmd.description}`);
    }
    
    console.log('\n示例:');
    console.log('  migration-cli status              # 查看迁移状态');
    console.log('  migration-cli migrate             # 执行迁移');
    console.log('  migration-cli rollback 2          # 回滚到版本 2');
    console.log('  migration-cli create add_indexes  # 创建新迁移');
  }

  /**
   * 生成迁移模板
   */
  private generateMigrationTemplate(version: number, name: string): string {
    const timestamp = new Date().toISOString().split('T')[0];
    
    return `-- 迁移 ${String(version).padStart(3, '0')}: ${name}
-- 创建时间: ${timestamp}
-- 描述: [请在此处添加迁移描述]

-- 向上迁移 (UP)
-- 在此处添加向上迁移的 SQL 语句

-- 示例:
-- CREATE TABLE example (
--     id TEXT PRIMARY KEY,
--     name TEXT NOT NULL,
--     created_at DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE INDEX IF NOT EXISTS idx_example_name ON example(name);

-- 向下迁移 (DOWN)
-- 在此处添加向下迁移的 SQL 语句（回滚操作）

-- 示例:
-- DROP INDEX IF EXISTS idx_example_name;
-- DROP TABLE IF EXISTS example;

-- 注意: SQLite 不支持 DROP COLUMN，如果需要删除列，请重建表`;
  }

  /**
   * 获取所有可用命令
   */
  getCommands(): MigrationCommand[] {
    return Array.from(this.commands.values());
  }
}

// 导出便捷函数
export async function runMigrationCommand(command: string, args: string[] = []): Promise<void> {
  const cli = new MigrationCLI();
  await cli.execute(command, args);
}

// 如果直接运行此文件，解析命令行参数
if (typeof process !== 'undefined' && process.argv) {
  const args = process.argv.slice(2);
  if (args.length > 0) {
    const command = args[0];
    const commandArgs = args.slice(1);
    
    runMigrationCommand(command, commandArgs).catch(error => {
      console.error('CLI 执行失败:', error);
      process.exit(1);
    });
  }
}
/**
 * 数据库迁移管理类
 * 负责数据库版本管理、迁移执行和回滚
 */

import type Database from '@tauri-apps/plugin-sql';
import { DatabaseBase } from './base';
import { MigrationLoader } from './migration-loader';
import { MigrationSafetyChecker } from './migration-safety';

// 迁移脚本接口
export interface Migration {
  version: number;
  name: string;
  description: string;
  up: string[];    // 升级 SQL 语句
  down: string[];  // 回滚 SQL 语句
}

// 迁移记录接口
export interface MigrationRecord {
  version: number;
  name: string;
  appliedAt: Date;
  checksum: string;
}

export class DatabaseMigration extends DatabaseBase {
  private migrations: Migration[] = [];
  private currentVersion: number = 0;
  private loader: MigrationLoader;
  private safetyChecker: MigrationSafetyChecker;

  constructor() {
    super();
    this.loader = new MigrationLoader();
    this.safetyChecker = new MigrationSafetyChecker();
  }

  /**
   * 加载所有迁移脚本
   */
  private async loadMigrations(): Promise<void> {
    if (this.migrations.length === 0) {
      this.migrations = await this.loader.loadAllMigrations();
    }
  }

  /**
   * 初始化迁移系统
   */
  async initializeMigrationSystem(): Promise<void> {
    this.ensureInitialized();

    try {
      // 加载迁移脚本
      await this.loadMigrations();
      
      // 创建迁移记录表
      await this.createMigrationTable();
      
      // 获取当前数据库版本
      this.currentVersion = await this.getCurrentVersion();
      
      console.log(`当前数据库版本: ${this.currentVersion}`);
    } catch (error) {
      throw new Error(`迁移系统初始化失败: ${error}`);
    }
  }

  /**
   * 创建迁移记录表
   */
  private async createMigrationTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        checksum TEXT NOT NULL
      )
    `;

    await this.db!.execute(sql);
  }

  /**
   * 获取当前数据库版本
   */
  async getCurrentVersion(): Promise<number> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT MAX(version) as version FROM migrations'
      );

      return result[0]?.version || 0;
    } catch (error) {
      // 如果迁移表不存在，返回版本 0
      return 0;
    }
  }

  /**
   * 获取所有已应用的迁移记录
   */
  async getAppliedMigrations(): Promise<MigrationRecord[]> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT version, name, applied_at, checksum FROM migrations ORDER BY version'
      );

      return result.map(row => ({
        version: row.version,
        name: row.name,
        appliedAt: new Date(row.applied_at),
        checksum: row.checksum,
      }));
    } catch (error) {
      return [];
    }
  }

  /**
   * 执行所有待应用的迁移
   */
  async migrate(): Promise<void> {
    this.ensureInitialized();

    try {
      await this.initializeMigrationSystem();

      const pendingMigrations = this.migrations.filter(
        migration => migration.version > this.currentVersion
      );

      if (pendingMigrations.length === 0) {
        console.log('数据库已是最新版本，无需迁移');
        return;
      }

      console.log(`发现 ${pendingMigrations.length} 个待应用的迁移`);

      // 验证所有待应用的迁移
      for (const migration of pendingMigrations) {
        const validation = this.loader.validateMigration(migration);
        if (!validation.valid) {
          throw new Error(`迁移 ${migration.version} 验证失败: ${validation.errors.join(', ')}`);
        }
      }

      // 安全检查
      const safetyReports = this.safetyChecker.checkMigrations(pendingMigrations);
      const failedReports = safetyReports.filter(report => !report.passed);
      
      if (failedReports.length > 0) {
        const errorMessages = failedReports.map(report => 
          `迁移 ${report.migration.version}: ${report.errors.map(e => e.message).join(', ')}`
        );
        throw new Error(`迁移安全检查失败: ${errorMessages.join('; ')}`);
      }

      // 显示警告
      const warnings = safetyReports.flatMap(report => report.warnings);
      if (warnings.length > 0) {
        console.warn('迁移安全警告:');
        warnings.forEach(warning => console.warn(`  - ${warning.message}`));
      }

      for (const migration of pendingMigrations) {
        await this.applyMigration(migration);
      }

      console.log('所有迁移已成功应用');
    } catch (error) {
      throw new Error(`数据库迁移失败: ${error}`);
    }
  }

  /**
   * 应用单个迁移
   */
  private async applyMigration(migration: Migration): Promise<void> {
    console.log(`应用迁移 ${migration.version}: ${migration.name}`);

    try {
      await this.beginTransaction();

      // 如果是初始迁移，执行完整的 schema
      if (migration.version === 1) {
        await this.executeInitialSchema();
      } else {
        // 执行迁移的 SQL 语句
        for (const sql of migration.up) {
          if (sql.trim()) {
            await this.db!.execute(sql);
          }
        }
      }

      // 记录迁移
      await this.recordMigration(migration);

      await this.commitTransaction();

      console.log(`迁移 ${migration.version} 应用成功`);
    } catch (error) {
      await this.rollbackTransaction();
      throw new Error(`应用迁移 ${migration.version} 失败: ${error}`);
    }
  }

  /**
   * 执行初始数据库 schema
   */
  private async executeInitialSchema(): Promise<void> {
    try {
      // 读取并执行 schema.sql
      const response = await fetch('/database/schema.sql');
      const schemaSQL = await response.text();
      
      // 分割 SQL 语句并执行
      const statements = schemaSQL
        .split(';')
        .map(s => s.trim())
        .filter(s => s.length > 0 && !s.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.trim()) {
          await this.db!.execute(statement);
        }
      }
    } catch (error) {
      throw new Error(`执行初始 schema 失败: ${error}`);
    }
  }

  /**
   * 记录迁移到数据库
   */
  private async recordMigration(migration: Migration): Promise<void> {
    const checksum = this.calculateChecksum(migration);

    await this.db!.execute(
      'INSERT INTO migrations (version, name, description, checksum) VALUES (?, ?, ?, ?)',
      [migration.version, migration.name, migration.description, checksum]
    );
  }

  /**
   * 计算迁移的校验和
   */
  private calculateChecksum(migration: Migration): string {
    const content = JSON.stringify({
      version: migration.version,
      name: migration.name,
      up: migration.up,
    });

    // 简单的校验和计算（在实际应用中可能需要更强的哈希算法）
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  /**
   * 回滚到指定版本
   */
  async rollbackTo(targetVersion: number): Promise<void> {
    this.ensureInitialized();

    if (targetVersion >= this.currentVersion) {
      throw new Error('目标版本必须小于当前版本');
    }

    try {
      const migrationsToRollback = this.migrations
        .filter(migration => migration.version > targetVersion)
        .sort((a, b) => b.version - a.version); // 降序排列

      console.log(`回滚到版本 ${targetVersion}，需要回滚 ${migrationsToRollback.length} 个迁移`);

      for (const migration of migrationsToRollback) {
        await this.rollbackMigration(migration);
      }

      this.currentVersion = targetVersion;
      console.log(`成功回滚到版本 ${targetVersion}`);
    } catch (error) {
      throw new Error(`回滚失败: ${error}`);
    }
  }

  /**
   * 回滚单个迁移
   */
  private async rollbackMigration(migration: Migration): Promise<void> {
    console.log(`回滚迁移 ${migration.version}: ${migration.name}`);

    try {
      await this.beginTransaction();

      // 执行回滚 SQL 语句
      for (const sql of migration.down) {
        if (sql.trim()) {
          await this.db!.execute(sql);
        }
      }

      // 删除迁移记录
      await this.db!.execute(
        'DELETE FROM migrations WHERE version = ?',
        [migration.version]
      );

      await this.commitTransaction();

      console.log(`迁移 ${migration.version} 回滚成功`);
    } catch (error) {
      await this.rollbackTransaction();
      throw new Error(`回滚迁移 ${migration.version} 失败: ${error}`);
    }
  }

  /**
   * 验证数据库完整性
   */
  async validateDatabase(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    this.ensureInitialized();

    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 检查所有必需的表是否存在
      const requiredTables = ['profiles', 'scenes', 'prompts', 'optimizations', 'settings'];
      
      for (const table of requiredTables) {
        const result = await this.db!.select<any[]>(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [table]
        );

        if (result.length === 0) {
          errors.push(`缺少必需的表: ${table}`);
        }
      }

      // 检查 FTS 表
      const ftsResult = await this.db!.select<any[]>(
        "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%_fts'"
      );

      if (ftsResult.length === 0) {
        warnings.push('未找到全文搜索索引表');
      }

      // 检查迁移记录的完整性
      const appliedMigrations = await this.getAppliedMigrations();
      const expectedMigrations = this.migrations.filter(m => m.version <= this.currentVersion);

      if (appliedMigrations.length !== expectedMigrations.length) {
        errors.push('迁移记录不完整');
      }

      // 验证校验和
      for (const applied of appliedMigrations) {
        const expected = this.migrations.find(m => m.version === applied.version);
        if (expected) {
          const expectedChecksum = this.calculateChecksum(expected);
          if (applied.checksum !== expectedChecksum) {
            errors.push(`迁移 ${applied.version} 的校验和不匹配`);
          }
        }
      }

    } catch (error) {
      errors.push(`验证过程中发生错误: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 获取数据库状态信息
   */
  async getDatabaseStatus(): Promise<{
    currentVersion: number;
    latestVersion: number;
    appliedMigrations: MigrationRecord[];
    pendingMigrations: Migration[];
    isUpToDate: boolean;
  }> {
    await this.initializeMigrationSystem();

    const appliedMigrations = await this.getAppliedMigrations();
    const latestVersion = Math.max(...this.migrations.map(m => m.version));
    const pendingMigrations = this.migrations.filter(m => m.version > this.currentVersion);

    return {
      currentVersion: this.currentVersion,
      latestVersion,
      appliedMigrations,
      pendingMigrations,
      isUpToDate: this.currentVersion === latestVersion,
    };
  }

  /**
   * 重置数据库（危险操作，仅用于开发和测试）
   */
  async resetDatabase(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('生产环境不允许重置数据库');
    }

    this.ensureInitialized();

    try {
      console.log('警告：正在重置数据库，所有数据将丢失');

      await this.beginTransaction();

      // 获取所有表名
      const tables = await this.db!.select<any[]>(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );

      // 删除所有表
      for (const table of tables) {
        await this.db!.execute(`DROP TABLE IF EXISTS ${table.name}`);
      }

      await this.commitTransaction();

      this.currentVersion = 0;
      console.log('数据库重置完成');
    } catch (error) {
      await this.rollbackTransaction();
      throw new Error(`数据库重置失败: ${error}`);
    }
  }
}
/**
 * 应用启动时的数据库初始化服务
 * 处理应用启动时的数据库检查、初始化和错误恢复
 */

import { dal } from './index';
import type { InitializationOptions } from './initializer';

export interface StartupOptions {
  // 是否在启动时自动修复数据库问题
  autoRepair?: boolean;
  // 是否在开发环境重置数据库
  resetInDevelopment?: boolean;
  // 初始化超时时间
  timeout?: number;
  // 错误回调
  onError?: (error: Error) => void;
  // 进度回调
  onProgress?: (step: string, progress: number) => void;
}

export interface StartupResult {
  success: boolean;
  initialized: boolean;
  repaired: boolean;
  duration: number;
  error?: string;
  details?: {
    version: number;
    migrationsApplied: number;
    seedDataInserted: boolean;
    repairActions?: string[];
  };
}

export class DatabaseStartupService {
  private options: Required<StartupOptions>;

  constructor(options: StartupOptions = {}) {
    this.options = {
      autoRepair: true,
      resetInDevelopment: false,
      timeout: 60_000, // 60秒
      onError: () => void 0,
      onProgress: () => void 0,
      ...options,
    };
  }

  /**
   * 执行数据库启动初始化
   */
  async startup(): Promise<StartupResult> {
    const startTime = Date.now();
    let repaired = false;
    let repairActions: string[] = [];

    try {
      this.options.onProgress('检查数据库状态...', 10);

      // 1. 检查数据库是否需要初始化
      const needsCheck = await dal.initializer.needsInitialization();
      
      if (!needsCheck.needsMigration && !needsCheck.needsSeedData) {
        // 数据库已经是最新状态，只需要连接
        this.options.onProgress('连接数据库...', 50);
        await dal.initialize(false);
        
        this.options.onProgress('验证数据库完整性...', 80);
        const status = await dal.initializer.getStatus();
        
        if (status.isInitialized) {
          this.options.onProgress('数据库启动完成', 100);
          return {
            success: true,
            initialized: false, // 已经初始化过了
            repaired: false,
            duration: Date.now() - startTime,
            details: {
              version: status.version,
              migrationsApplied: 0,
              seedDataInserted: false,
            },
          };
        }
      }

      // 2. 需要初始化或修复
      this.options.onProgress('初始化数据库...', 30);

      const initOptions: InitializationOptions = {
        runMigrations: needsCheck.needsMigration,
        insertSeedData: needsCheck.needsSeedData,
        validateDatabase: true,
        resetInDevelopment: this.options.resetInDevelopment,
        timeout: this.options.timeout,
      };

      const result = await dal.initializer.initialize(initOptions);

      if (!result.success) {
        // 初始化失败，尝试修复
        if (this.options.autoRepair) {
          this.options.onProgress('初始化失败，尝试修复...', 60);
          const repairResult = await dal.initializer.repair();
          
          if (repairResult.success) {
            repaired = true;
            repairActions = repairResult.actionsPerformed;
            this.options.onProgress('修复完成，重新初始化...', 80);
            
            // 修复后重新初始化
            const retryResult = await dal.initializer.initialize(initOptions);
            if (!retryResult.success) {
              throw new Error(`修复后重新初始化失败: ${retryResult.error}`);
            }
            
            this.options.onProgress('数据库启动完成', 100);
            return {
              success: true,
              initialized: true,
              repaired: true,
              duration: Date.now() - startTime,
              details: {
                version: retryResult.version,
                migrationsApplied: retryResult.migrationsApplied,
                seedDataInserted: retryResult.seedDataInserted,
                repairActions,
              },
            };
          } else {
            throw new Error(`数据库修复失败: ${repairResult.error}`);
          }
        } else {
          throw new Error(`数据库初始化失败: ${result.error}`);
        }
      }

      this.options.onProgress('数据库启动完成', 100);
      return {
        success: true,
        initialized: true,
        repaired,
        duration: Date.now() - startTime,
        details: {
          version: result.version,
          migrationsApplied: result.migrationsApplied,
          seedDataInserted: result.seedDataInserted,
          repairActions: repaired ? repairActions : undefined,
        },
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.options.onError(error instanceof Error ? error : new Error(errorMessage));
      
      return {
        success: false,
        initialized: false,
        repaired,
        duration,
        error: errorMessage,
        details: repaired ? { 
          version: 0,
          migrationsApplied: 0,
          seedDataInserted: false,
          repairActions 
        } : undefined,
      };
    }
  }

  /**
   * 检查数据库健康状态
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // 检查连接状态
      const connectionStatus = dal.getConnectionStatus();
      if (!connectionStatus.isConnected) {
        issues.push('数据库连接断开');
        recommendations.push('重新启动应用');
      }

      // 检查数据库状态
      const status = await dal.initializer.getStatus();
      if (!status.isInitialized) {
        issues.push('数据库未正确初始化');
        recommendations.push('运行数据库修复');
      }

      if (status.validationResult && !status.validationResult.isValid) {
        issues.push(...status.validationResult.errors);
        recommendations.push('运行数据库修复');
      }

      // 检查种子数据
      if (!status.hasSeedData) {
        issues.push('缺少默认数据');
        recommendations.push('重新插入种子数据');
      }

      return {
        healthy: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      return {
        healthy: false,
        issues: [error instanceof Error ? error.message : String(error)],
        recommendations: ['重新启动应用', '检查数据库文件权限'],
      };
    }
  }

  /**
   * 执行数据库修复
   */
  async repair(): Promise<{
    success: boolean;
    actions: string[];
    error?: string;
  }> {
    try {
      const result = await dal.initializer.repair();
      return {
        success: result.success,
        actions: result.actionsPerformed,
        error: result.error,
      };
    } catch (error) {
      return {
        success: false,
        actions: [],
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 创建数据库备份
   */
  async backup(): Promise<{
    success: boolean;
    backupPath?: string;
    error?: string;
  }> {
    try {
      return await dal.initializer.createBackup();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStatistics(): Promise<{
    version: number;
    tables: Record<string, number>;
    totalRecords: number;
    databaseSize?: number;
    lastBackup?: Date;
  }> {
    try {
      const status = await dal.initializer.getStatus();
      const stats = await dal.getStats();

      const tables = {
        profiles: stats.profiles.total,
        scenes: stats.scenes.total,
        prompts: stats.prompts.total,
        optimizations: stats.optimizations.total,
      };

      const totalRecords = Object.values(tables).reduce((sum, count) => sum + count, 0);

      return {
        version: status.version,
        tables,
        totalRecords,
        // databaseSize 和 lastBackup 可以在后续实现
      };
    } catch (error) {
      return {
        version: 0,
        tables: {},
        totalRecords: 0,
      };
    }
  }
}

// 导出默认实例
export const databaseStartup = new DatabaseStartupService();

// 导出便捷函数
export async function initializeDatabase(options?: StartupOptions): Promise<StartupResult> {
  const service = new DatabaseStartupService(options);
  return service.startup();
}

export async function checkDatabaseHealth() {
  return databaseStartup.healthCheck();
}

export async function repairDatabase() {
  return databaseStartup.repair();
}
/**
 * 数据库基础连接类
 * 处理数据库连接、初始化和基础操作
 */

import Database from '@tauri-apps/plugin-sql';
import { DATABASE_CONFIG, ERROR_MESSAGES, PERFORMANCE_CONFIG } from './config';

// 导入数据库架构文件
import schemaSQL from '@/database/schema.sql?raw';

export class DatabaseBase {
  protected db: Database | null = null;
  protected isInitialized = false;

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      this.db = await Database.load(`sqlite:${DATABASE_CONFIG.filename}`);
      await this.configureDatabase();
      await this.executeSchema();
      this.isInitialized = true;
      
      if (PERFORMANCE_CONFIG.enableQueryLogging) {
        console.log('数据库初始化成功');
      }
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw new Error(`${ERROR_MESSAGES.CONNECTION_FAILED}: ${error}`);
    }
  }

  /**
   * 配置数据库参数
   */
  private async configureDatabase(): Promise<void> {
    if (!this.db) throw new Error(ERROR_MESSAGES.NOT_INITIALIZED);

    const config = DATABASE_CONFIG.connection;
    
    try {
      // 配置数据库参数
      await this.db.execute(`PRAGMA foreign_keys = ${config.foreignKeys ? 'ON' : 'OFF'}`);
      await this.db.execute(`PRAGMA journal_mode = ${config.journalMode}`);
      await this.db.execute(`PRAGMA synchronous = ${config.synchronous}`);
      await this.db.execute(`PRAGMA cache_size = ${config.cacheSize}`);
      await this.db.execute(`PRAGMA temp_store = ${config.tempStore}`);
    } catch (error) {
      throw new Error(`数据库配置失败: ${error}`);
    }
  }

  /**
   * 执行数据库架构初始化
   */
  private async executeSchema(): Promise<void> {
    if (!this.db) {
      throw new Error(ERROR_MESSAGES.NOT_INITIALIZED);
    }

    try {
      // 直接使用导入的SQL文件内容
      await this.db.execute(schemaSQL);
      
      if (PERFORMANCE_CONFIG.enableQueryLogging) {
        console.log('数据库架构初始化成功');
      }
    } catch (error) {
      console.error('数据库架构初始化失败:', error);
      throw new Error(`${ERROR_MESSAGES.SCHEMA_INIT_FAILED}: ${error}`);
    }
  }

  /**
   * 确保数据库已初始化
   */
  protected ensureInitialized(): void {
    if (!this.isInitialized || !this.db) {
      throw new Error(ERROR_MESSAGES.NOT_INITIALIZED);
    }
  }

  /**
   * 生成 UUID
   */
  protected generateId(): string {
    return crypto.randomUUID();
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): Database {
    if (!this.db || !this.isInitialized) {
      throw new Error(ERROR_MESSAGES.NOT_INITIALIZED);
    }
    return this.db;
  }

  /**
   * 获取数据库连接状态
   */
  getConnectionStatus(): { isConnected: boolean; isInitialized: boolean } {
    return {
      isConnected: this.db !== null,
      isInitialized: this.isInitialized,
    };
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      this.isInitialized = false;
    }
  }

  /**
   * 开始事务
   */
  protected async beginTransaction(): Promise<void> {
    this.ensureInitialized();
    await this.db!.execute('BEGIN TRANSACTION');
  }

  /**
   * 提交事务
   */
  protected async commitTransaction(): Promise<void> {
    this.ensureInitialized();
    await this.db!.execute('COMMIT');
  }

  /**
   * 回滚事务
   */
  protected async rollbackTransaction(): Promise<void> {
    this.ensureInitialized();
    await this.db!.execute('ROLLBACK');
  }
}
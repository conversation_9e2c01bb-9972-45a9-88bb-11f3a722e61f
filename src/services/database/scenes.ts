/**
 * 场景模板数据访问层
 */

import { DatabaseBase } from './base';
import { 
  SceneSchema,
  type Scene,
  type CreateScene,
  type UpdateScene
} from '@/schemas';

export class ScenesRepository extends DatabaseBase {
  /**
   * 创建场景模板
   */
  async create(data: CreateScene): Promise<Scene> {
    this.ensureInitialized();

    const scene: Scene = {
      ...data,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = SceneSchema.parse(scene);
    
    try {
      await this.db!.execute(
        `INSERT INTO scenes (id, title, description, template, variables, category, is_built_in, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.title,
          validated.description,
          validated.template,
          JSON.stringify(validated.variables),
          validated.category,
          validated.isBuiltIn,
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );
      
      return validated;
    } catch (error) {
      throw new Error(`创建场景模板失败: ${error}`);
    }
  }

  /**
   * 获取场景模板
   */
  async getById(id: string): Promise<Scene | null> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM scenes WHERE id = ? AND is_deleted = FALSE',
        [id]
      );

      if (result.length === 0) return null;

      return this.mapRowToScene(result[0]);
    } catch (error) {
      throw new Error(`获取场景模板失败: ${error}`);
    }
  }

  /**
   * 获取所有场景模板
   */
  async getAll(options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    category?: string;
  }): Promise<{
    items: Scene[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    this.ensureInitialized();

    try {
      let query = 'SELECT * FROM scenes WHERE is_deleted = FALSE';
      let countQuery = 'SELECT COUNT(*) as total FROM scenes WHERE is_deleted = FALSE';
      const params: any[] = [];

      // 添加分类筛选
      if (options?.category) {
        query += ' AND category = ?';
        countQuery += ' AND category = ?';
        params.push(options.category);
      }

      // 添加排序
      if (options?.sortBy) {
        const sortOrder = options.sortOrder || 'asc';
        query += ` ORDER BY ${options.sortBy} ${sortOrder}`;
      } else {
        query += ' ORDER BY created_at DESC';
      }

      // 添加分页
      const paginationParams = [...params];
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        query += ' LIMIT ? OFFSET ?';
        paginationParams.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(query, paginationParams),
        this.db!.select<any[]>(countQuery, params)
      ]);

      const items = result.map(row => this.mapRowToScene(row));
      const total = countResult[0].total;
      const page = options?.page || 1;
      const limit = options?.limit || total;

      return {
        items,
        total,
        page,
        limit,
        totalPages: limit > 0 ? Math.ceil(total / limit) : 1,
      };
    } catch (error) {
      throw new Error(`获取场景模板列表失败: ${error}`);
    }
  }

  /**
   * 按分类获取场景模板
   */
  async getByCategory(category: string): Promise<Scene[]> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM scenes WHERE category = ? AND is_deleted = FALSE ORDER BY title',
        [category]
      );

      return result.map(row => this.mapRowToScene(row));
    } catch (error) {
      throw new Error(`按分类获取场景模板失败: ${error}`);
    }
  }

  /**
   * 获取内置场景模板
   */
  async getBuiltIn(): Promise<Scene[]> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM scenes WHERE is_built_in = TRUE AND is_deleted = FALSE ORDER BY title'
      );

      return result.map(row => this.mapRowToScene(row));
    } catch (error) {
      throw new Error(`获取内置场景模板失败: ${error}`);
    }
  }

  /**
   * 更新场景模板
   */
  async update(id: string, data: UpdateScene): Promise<Scene> {
    this.ensureInitialized();

    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('场景模板不存在');
    }

    const updated = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    const validated = SceneSchema.parse(updated);

    try {
      await this.db!.execute(
        `UPDATE scenes 
         SET title = ?, description = ?, template = ?, variables = ?, category = ?, is_built_in = ?, updated_at = ?
         WHERE id = ?`,
        [
          validated.title,
          validated.description,
          validated.template,
          JSON.stringify(validated.variables),
          validated.category,
          validated.isBuiltIn,
          validated.updatedAt.toISOString(),
          id,
        ]
      );

      return validated;
    } catch (error) {
      throw new Error(`更新场景模板失败: ${error}`);
    }
  }

  /**
   * 软删除场景模板
   */
  async delete(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      const result = await this.db!.execute(
        'UPDATE scenes SET is_deleted = TRUE, updated_at = ? WHERE id = ? AND is_deleted = FALSE',
        [new Date().toISOString(), id]
      );
      
      if (result.rowsAffected === 0) {
        throw new Error('场景模板不存在或已被删除');
      }
    } catch (error) {
      throw new Error(`删除场景模板失败: ${error}`);
    }
  }

  /**
   * 获取所有分类
   */
  async getCategories(): Promise<string[]> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT DISTINCT category FROM scenes WHERE is_deleted = FALSE ORDER BY category'
      );

      return result.map(row => row.category);
    } catch (error) {
      throw new Error(`获取分类列表失败: ${error}`);
    }
  }

  /**
   * 将数据库行映射为 Scene 对象
   */
  private mapRowToScene(row: any): Scene {
    try {
      return SceneSchema.parse({
        id: row.id,
        title: row.title,
        description: row.description || '',
        template: row.template,
        variables: JSON.parse(row.variables || '[]'),
        category: row.category,
        isBuiltIn: Boolean(row.is_built_in),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      });
    } catch (error) {
      throw new Error(`数据格式错误：场景模板 - ${error}`);
    }
  }
}
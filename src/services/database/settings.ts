/**
 * 应用设置数据访问层
 */

import { DatabaseBase } from './base';
import { 
  AppSettingsSchema,
  type AppSettings,
  type UpdateAppSettings,
  DEFAULT_VALUES
} from '@/schemas';

export class SettingsRepository extends DatabaseBase {
  /**
   * 获取应用设置
   */
  async get(): Promise<AppSettings> {
    this.ensureInitialized();
        
    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM settings WHERE id = \"app\" LIMIT 1'
      );
      
      if (result.length === 0) {
        // 创建默认设置
        return this.createDefault();
      }

      return this.mapRowToSettings(result[0]);
    } catch (error) {
      throw new Error(`获取应用设置失败: ${error}`);
    }
  }

  /**
   * 更新应用设置
   */
  async update(data: UpdateAppSettings): Promise<AppSettings> {
    this.ensureInitialized();

    try {
      const existing = await this.get();
      
      const updated = {
        ...existing,
        ...data,
        updatedAt: new Date(),
      };

      const validated = AppSettingsSchema.parse(updated);

      await this.db!.execute(
        `UPDATE settings 
         SET theme = ?, language = ?, shortcuts = ?, ai_config = ?, auto_save = ?, backup_enabled = ?, updated_at = ?
         WHERE id = ?`,
        [
          validated.theme,
          validated.language,
          JSON.stringify(validated.shortcuts),
          JSON.stringify(validated.aiConfig),
          validated.autoSave,
          validated.backupEnabled,
          validated.updatedAt.toISOString(),
          validated.id,
        ]
      );

      return validated;
    } catch (error) {
      throw new Error(`更新应用设置失败: ${error}`);
    }
  }

  /**
   * 重置为默认设置
   */
  async reset(): Promise<AppSettings> {
    this.ensureInitialized();

    try {
      // 删除现有设置
      await this.db!.execute('DELETE FROM settings WHERE id = \"app\"');
      
      // 创建默认设置
      return this.createDefault();
    } catch (error) {
      throw new Error(`重置应用设置失败: ${error}`);
    }
  }

  /**
   * 创建默认设置
   */
  private async createDefault(): Promise<AppSettings> {
    const defaultSettings: AppSettings = {
      id: 'app',
      theme: DEFAULT_VALUES.THEME,
      language: DEFAULT_VALUES.LANGUAGE,
      shortcuts: DEFAULT_VALUES.SHORTCUTS,
      aiConfig: {
        model: DEFAULT_VALUES.AI_MODEL,
        temperature: DEFAULT_VALUES.AI_TEMPERATURE,
        maxTokens: DEFAULT_VALUES.AI_MAX_TOKENS,
      },
      autoSave: DEFAULT_VALUES.AUTO_SAVE,
      backupEnabled: DEFAULT_VALUES.BACKUP_ENABLED,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const validated = AppSettingsSchema.parse(defaultSettings);

    try {
      await this.db!.execute(
        `INSERT INTO settings (id, theme, language, shortcuts, ai_config, auto_save, backup_enabled, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.theme,
          validated.language,
          JSON.stringify(validated.shortcuts),
          JSON.stringify(validated.aiConfig),
          validated.autoSave,
          validated.backupEnabled,
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );

      return validated;
    } catch (error) {
      throw new Error(`创建默认设置失败: ${error}`);
    }
  }

  /**
   * 更新快捷键配置
   */
  async updateShortcuts(shortcuts: AppSettings['shortcuts']): Promise<AppSettings> {
    return this.update({ shortcuts });
  }

  /**
   * 更新 AI 配置
   */
  async updateAIConfig(aiConfig: AppSettings['aiConfig']): Promise<AppSettings> {
    return this.update({ aiConfig });
  }

  /**
   * 更新主题
   */
  async updateTheme(theme: AppSettings['theme']): Promise<AppSettings> {
    return this.update({ theme });
  }

  /**
   * 更新语言
   */
  async updateLanguage(language: string): Promise<AppSettings> {
    return this.update({ language });
  }

  /**
   * 将数据库行映射为 AppSettings 对象
   */
  private mapRowToSettings(row: any): AppSettings {
    try {
      return AppSettingsSchema.parse({
        id: row.id,
        theme: row.theme,
        language: row.language,
        shortcuts: JSON.parse(row.shortcuts),
        aiConfig: JSON.parse(row.ai_config),
        autoSave: Boolean(row.auto_save),
        backupEnabled: Boolean(row.backup_enabled),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      });
    } catch (error) {
      throw new Error(`数据格式错误：应用设置 - ${error}`);
    }
  }
}
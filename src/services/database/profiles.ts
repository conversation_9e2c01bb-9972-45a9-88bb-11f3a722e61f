/**
 * 个人画像数据访问层
 */

import { DatabaseBase } from './base';
import { 
  ProfileSchema, 
  type Profile,
  type CreateProfile,
  type UpdateProfile
} from '@/schemas';

export class ProfilesRepository extends DatabaseBase {
  /**
   * 创建个人画像
   */
  async create(data: CreateProfile): Promise<Profile> {
    this.ensureInitialized();

    const profile: Profile = {
      ...data,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = ProfileSchema.parse(profile);
    
    try {
      await this.db!.execute(
        `INSERT INTO profiles (id, name, role, skills, tone, is_active, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.name,
          validated.role,
          JSON.stringify(validated.skills),
          validated.tone,
          validated.isActive,
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );
      
      return validated;
    } catch (error) {
      throw new Error(`创建个人画像失败: ${error}`);
    }
  }

  /**
   * 获取个人画像
   */
  async getById(id: string): Promise<Profile | null> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM profiles WHERE id = ? AND is_deleted = FALSE',
        [id]
      );

      if (result.length === 0) return null;

      return this.mapRowToProfile(result[0]);
    } catch (error) {
      throw new Error(`获取个人画像失败: ${error}`);
    }
  }

  /**
   * 获取所有个人画像
   */
  async getAll(options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    items: Profile[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    this.ensureInitialized();

    try {
      let query = 'SELECT * FROM profiles WHERE is_deleted = FALSE';
      let countQuery = 'SELECT COUNT(*) as total FROM profiles WHERE is_deleted = FALSE';
      const params: any[] = [];

      // 添加排序
      if (options?.sortBy) {
        const sortOrder = options.sortOrder || 'asc';
        query += ` ORDER BY ${options.sortBy} ${sortOrder}`;
      } else {
        query += ' ORDER BY created_at DESC';
      }

      // 添加分页
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        query += ' LIMIT ? OFFSET ?';
        params.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(query, params),
        this.db!.select<any[]>(countQuery)
      ]);

      const items = result.map(row => this.mapRowToProfile(row));
      const total = countResult[0].total;
      const page = options?.page || 1;
      const limit = options?.limit || total;

      return {
        items,
        total,
        page,
        limit,
        totalPages: limit > 0 ? Math.ceil(total / limit) : 1,
      };
    } catch (error) {
      throw new Error(`获取个人画像列表失败: ${error}`);
    }
  }

  /**
   * 更新个人画像
   */
  async update(id: string, data: UpdateProfile): Promise<Profile> {
    this.ensureInitialized();

    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('画像不存在');
    }

    const updated = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    const validated = ProfileSchema.parse(updated);

    try {
      await this.db!.execute(
        `UPDATE profiles 
         SET name = ?, role = ?, skills = ?, tone = ?, is_active = ?, updated_at = ?
         WHERE id = ?`,
        [
          validated.name,
          validated.role,
          JSON.stringify(validated.skills),
          validated.tone,
          validated.isActive,
          validated.updatedAt.toISOString(),
          id,
        ]
      );

      return validated;
    } catch (error) {
      throw new Error(`更新个人画像失败: ${error}`);
    }
  }

  /**
   * 软删除个人画像
   */
  async delete(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      const result = await this.db!.execute(
        'UPDATE profiles SET is_deleted = TRUE, updated_at = ? WHERE id = ? AND is_deleted = FALSE',
        [new Date().toISOString(), id]
      );
      
      if (result.rowsAffected === 0) {
        throw new Error('画像不存在或已被删除');
      }
    } catch (error) {
      throw new Error(`删除个人画像失败: ${error}`);
    }
  }

  /**
   * 获取活跃的个人画像
   */
  async getActive(): Promise<Profile | null> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM profiles WHERE is_active = TRUE AND is_deleted = FALSE LIMIT 1'
      );

      if (result.length === 0) return null;

      return this.mapRowToProfile(result[0]);
    } catch (error) {
      throw new Error(`获取活跃画像失败: ${error}`);
    }
  }

  /**
   * 设置活跃的个人画像
   */
  async setActive(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      await this.beginTransaction();

      // 取消所有画像的活跃状态
      await this.db!.execute('UPDATE profiles SET is_active = FALSE WHERE is_deleted = FALSE');

      // 设置指定画像为活跃状态
      const result = await this.db!.execute(
        'UPDATE profiles SET is_active = TRUE WHERE id = ? AND is_deleted = FALSE',
        [id]
      );

      if (result.rowsAffected === 0) {
        await this.rollbackTransaction();
        throw new Error('画像不存在');
      }

      await this.commitTransaction();
    } catch (error) {
      await this.rollbackTransaction();
      throw new Error(`设置活跃画像失败: ${error}`);
    }
  }

  /**
   * 将数据库行映射为 Profile 对象
   */
  private mapRowToProfile(row: any): Profile {
    try {
      return ProfileSchema.parse({
        id: row.id,
        name: row.name,
        role: row.role,
        skills: JSON.parse(row.skills),
        tone: row.tone,
        isActive: Boolean(row.is_active),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      });
    } catch (error) {
      throw new Error(`数据格式错误：个人画像 - ${error}`);
    }
  }
}
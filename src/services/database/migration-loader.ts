/**
 * 迁移脚本加载器
 * 负责从文件系统加载和解析迁移脚本
 */

import type { Migration } from './migration';

export interface MigrationFile {
  version: number;
  filename: string;
  content: string;
}

export class MigrationLoader {
  private migrationCache: Map<number, Migration> = new Map();

  /**
   * 加载所有迁移脚本
   */
  async loadAllMigrations(): Promise<Migration[]> {
    const migrations: Migration[] = [];

    // 手动定义迁移脚本（在实际应用中，这些可能从文件系统动态加载）
    const migrationDefinitions = [
      {
        version: 1,
        name: 'initial_schema',
        description: '创建初始数据库结构',
        filename: '001_initial.sql',
      },
      {
        version: 2,
        name: 'add_usage_tracking',
        description: '添加使用跟踪功能',
        filename: '002_add_usage_tracking.sql',
      },
      {
        version: 3,
        name: 'add_user_preferences',
        description: '添加用户偏好设置',
        filename: '003_add_user_preferences.sql',
      },
    ];

    for (const def of migrationDefinitions) {
      try {
        const migration = await this.loadMigration(def);
        migrations.push(migration);
        this.migrationCache.set(migration.version, migration);
      } catch (error) {
        console.warn(`加载迁移 ${def.filename} 失败:`, error);
      }
    }

    return migrations.sort((a, b) => a.version - b.version);
  }

  /**
   * 加载单个迁移脚本
   */
  private async loadMigration(definition: {
    version: number;
    name: string;
    description: string;
    filename: string;
  }): Promise<Migration> {
    // 检查缓存
    if (this.migrationCache.has(definition.version)) {
      return this.migrationCache.get(definition.version)!;
    }

    let upStatements: string[] = [];
    let downStatements: string[] = [];

    try {
      if (definition.version === 1) {
        // 初始迁移从 schema.sql 加载
        upStatements = await this.loadInitialSchema();
        downStatements = this.getInitialSchemaDownStatements();
      } else {
        // 其他迁移从对应的 SQL 文件加载
        const content = await this.loadMigrationFile(definition.filename);
        const parsed = this.parseMigrationContent(content);
        upStatements = parsed.up;
        downStatements = parsed.down;
      }
    } catch (error) {
      console.warn(`解析迁移文件 ${definition.filename} 失败:`, error);
      // 如果文件加载失败，使用预定义的语句
      const fallback = this.getFallbackMigration(definition.version);
      upStatements = fallback.up;
      downStatements = fallback.down;
    }

    return {
      version: definition.version,
      name: definition.name,
      description: definition.description,
      up: upStatements,
      down: downStatements,
    };
  }

  /**
   * 加载初始 schema
   */
  private async loadInitialSchema(): Promise<string[]> {
    try {
      const response = await fetch('/database/schema.sql');
      const content = await response.text();
      
      return this.splitSqlStatements(content);
    } catch (error) {
      console.warn('加载 schema.sql 失败，使用内置 schema');
      return this.getBuiltinInitialSchema();
    }
  }

  /**
   * 加载迁移文件
   */
  private async loadMigrationFile(filename: string): Promise<string> {
    try {
      const response = await fetch(`/database/migrations/${filename}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.text();
    } catch (error) {
      throw new Error(`无法加载迁移文件 ${filename}: ${error}`);
    }
  }

  /**
   * 解析迁移文件内容
   */
  private parseMigrationContent(content: string): { up: string[]; down: string[] } {
    const lines = content.split('\n');
    const up: string[] = [];
    const down: string[] = [];
    
    let currentSection: 'up' | 'down' | 'none' = 'none';
    let currentStatement = '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过注释和空行
      if (trimmedLine.startsWith('--') || trimmedLine === '') {
        // 检查是否是特殊注释标记
        if (trimmedLine.includes('向上迁移') || trimmedLine.includes('UP')) {
          currentSection = 'up';
        } else if (trimmedLine.includes('向下迁移') || trimmedLine.includes('DOWN')) {
          currentSection = 'down';
        }
        continue;
      }

      currentStatement += line + '\n';

      // 如果行以分号结尾，表示语句结束
      if (trimmedLine.endsWith(';')) {
        const statement = currentStatement.trim();
        if (statement && currentSection !== 'none') {
          if (currentSection === 'up') {
            up.push(statement);
          } else if (currentSection === 'down') {
            down.push(statement);
          }
        }
        currentStatement = '';
      }
    }

    return { up, down };
  }

  /**
   * 分割 SQL 语句
   */
  private splitSqlStatements(content: string): string[] {
    return content
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
      .map(s => s + ';'); // 重新添加分号
  }

  /**
   * 获取初始 schema 的回滚语句
   */
  private getInitialSchemaDownStatements(): string[] {
    return [
      'DROP VIEW IF EXISTS daily_optimization_stats;',
      'DROP VIEW IF EXISTS scene_usage_stats;',
      'DROP VIEW IF EXISTS profile_usage_stats;',
      'DROP VIEW IF EXISTS favorite_prompts_per_profile;',
      'DROP VIEW IF EXISTS recent_optimizations;',
      'DROP VIEW IF EXISTS built_in_scenes;',
      'DROP VIEW IF EXISTS favorite_prompts;',
      'DROP VIEW IF EXISTS active_profiles;',
      'DROP TABLE IF EXISTS optimizations_fts;',
      'DROP TABLE IF EXISTS prompts_fts;',
      'DROP TABLE IF EXISTS settings;',
      'DROP TABLE IF EXISTS optimizations;',
      'DROP TABLE IF EXISTS prompts;',
      'DROP TABLE IF EXISTS scenes;',
      'DROP TABLE IF EXISTS profiles;',
    ];
  }

  /**
   * 获取内置的初始 schema（备用方案）
   */
  private getBuiltinInitialSchema(): string[] {
    return [
      `CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        skills TEXT NOT NULL,
        tone TEXT NOT NULL,
        is_active BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );`,
      
      `CREATE TABLE IF NOT EXISTS scenes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT DEFAULT '',
        template TEXT NOT NULL,
        variables TEXT DEFAULT '[]',
        category TEXT NOT NULL,
        is_built_in BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );`,
      
      `CREATE TABLE IF NOT EXISTS prompts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        tags TEXT DEFAULT '[]',
        scene_id TEXT,
        profile_id TEXT,
        usage_count INTEGER DEFAULT 0,
        is_favorite BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
      );`,
      
      `CREATE TABLE IF NOT EXISTS optimizations (
        id TEXT PRIMARY KEY,
        original TEXT NOT NULL,
        optimized TEXT NOT NULL,
        scene_id TEXT,
        profile_id TEXT,
        prompt_id TEXT,
        model_used TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT NOT NULL,
        is_deleted BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL,
        FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE SET NULL
      );`,
      
      `CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY DEFAULT 'app',
        theme TEXT DEFAULT 'system',
        language TEXT DEFAULT 'zh-CN',
        shortcuts TEXT NOT NULL,
        ai_config TEXT NOT NULL,
        auto_save BOOLEAN DEFAULT TRUE,
        backup_enabled BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );`,
    ];
  }

  /**
   * 获取备用迁移定义
   */
  private getFallbackMigration(version: number): { up: string[]; down: string[] } {
    switch (version) {
      case 2:
        return {
          up: [
            'ALTER TABLE prompts ADD COLUMN usage_count INTEGER DEFAULT 0;',
            'ALTER TABLE prompts ADD COLUMN last_used_at DATETIME;',
            'CREATE INDEX IF NOT EXISTS idx_prompts_usage_count ON prompts(usage_count DESC);',
          ],
          down: [
            'DROP INDEX IF EXISTS idx_prompts_usage_count;',
            // SQLite 不支持 DROP COLUMN，需要重建表
          ],
        };
      
      case 3:
        return {
          up: [
            'ALTER TABLE settings ADD COLUMN ui_preferences TEXT DEFAULT \'{}\';',
            'ALTER TABLE settings ADD COLUMN notification_settings TEXT DEFAULT \'{}\';',
          ],
          down: [
            // SQLite 不支持 DROP COLUMN，需要重建表
          ],
        };
      
      default:
        return { up: [], down: [] };
    }
  }

  /**
   * 验证迁移脚本
   */
  validateMigration(migration: Migration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查版本号
    if (!Number.isInteger(migration.version) || migration.version <= 0) {
      errors.push('版本号必须是正整数');
    }

    // 检查名称
    if (!migration.name || migration.name.trim().length === 0) {
      errors.push('迁移名称不能为空');
    }

    // 检查 SQL 语句
    if (!migration.up || migration.up.length === 0) {
      errors.push('必须包含向上迁移语句');
    }

    // 验证 SQL 语法（基本检查）
    for (const statement of migration.up) {
      if (!this.isValidSqlStatement(statement)) {
        errors.push(`无效的 SQL 语句: ${statement.substring(0, 50)}...`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 基本的 SQL 语句验证
   */
  private isValidSqlStatement(statement: string): boolean {
    const trimmed = statement.trim();
    
    // 检查是否为空
    if (!trimmed) return false;
    
    // 检查是否以分号结尾
    if (!trimmed.endsWith(';')) return false;
    
    // 检查是否包含基本的 SQL 关键字
    const sqlKeywords = [
      'CREATE', 'ALTER', 'DROP', 'INSERT', 'UPDATE', 'DELETE', 'SELECT'
    ];
    
    const upperStatement = trimmed.toUpperCase();
    const hasValidKeyword = sqlKeywords.some(keyword => 
      upperStatement.startsWith(keyword)
    );
    
    return hasValidKeyword;
  }

  /**
   * 获取缓存的迁移
   */
  getCachedMigration(version: number): Migration | undefined {
    return this.migrationCache.get(version);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.migrationCache.clear();
  }
}
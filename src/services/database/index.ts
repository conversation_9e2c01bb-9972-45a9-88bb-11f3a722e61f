/**
 * 数据访问层统一入口
 * 提供所有数据库操作的统一接口
 */

import { DatabaseBase } from './base';
import { ProfilesRepository } from './profiles';
import { ScenesRepository } from './scenes';
import { PromptsRepository } from './prompts';
import { OptimizationsRepository } from './optimizations';
import { SettingsRepository } from './settings';
import { DatabaseSeeder } from './seed';
import { DatabaseInitializer, type InitializationOptions } from './initializer';

export class DataAccessLayer extends DatabaseBase {
  public profiles: ProfilesRepository;
  public scenes: ScenesRepository;
  public prompts: PromptsRepository;
  public optimizations: OptimizationsRepository;
  public settings: SettingsRepository;
  public seeder: DatabaseSeeder;
  public initializer: DatabaseInitializer;

  constructor() {
    super();
    
    // 初始化各个仓库，共享数据库连接
    this.profiles = new ProfilesRepository();
    this.scenes = new ScenesRepository();
    this.prompts = new PromptsRepository();
    this.optimizations = new OptimizationsRepository();
    this.settings = new SettingsRepository();
    this.seeder = new DatabaseSeeder(this);
    this.initializer = new DatabaseInitializer(this);
  }

  /**
   * 初始化所有仓库（简单版本，用于向后兼容）
   */
  async initialize(seedData: boolean = true): Promise<void> {
    // 先初始化基础连接
    await super.initialize();
    
    // 然后为所有仓库设置相同的数据库连接
    const repositories = [
      this.profiles,
      this.scenes,
      this.prompts,
      this.optimizations,
      this.settings,
    ];

    for (const repo of repositories) {
      // 直接设置数据库连接，避免重复初始化
      (repo as any).db = this.db;
      (repo as any).isInitialized = this.isInitialized;
    }

    // 插入种子数据（如果需要）
    if (seedData) {
      try {
        await this.seeder.seedAll();
      } catch (error) {
        console.warn('种子数据插入失败，但不影响应用启动:', error);
      }
    }
  }

  /**
   * 完整的数据库初始化（推荐使用）
   */
  async initializeWithMigrations(options?: InitializationOptions): Promise<void> {
    const result = await this.initializer.initialize(options);
    
    if (!result.success) {
      throw new Error(`数据库初始化失败: ${result.error}`);
    }

    // 为所有仓库设置数据库连接
    const repositories = [
      this.profiles,
      this.scenes,
      this.prompts,
      this.optimizations,
      this.settings,
    ];

    for (const repo of repositories) {
      (repo as any).db = this.db;
      (repo as any).isInitialized = this.isInitialized;
    }

    console.log(`数据库初始化完成 - 版本: ${result.version}, 迁移: ${result.migrationsApplied}, 种子数据: ${result.seedDataInserted}`);
  }

  /**
   * 关闭所有连接
   */
  async close(): Promise<void> {
    await super.close();
    
    // 清理所有仓库的连接状态
    const repositories = [
      this.profiles,
      this.scenes,
      this.prompts,
      this.optimizations,
      this.settings,
    ];

    for (const repo of repositories) {
      (repo as any).db = null;
      (repo as any).isInitialized = false;
    }
  }

  /**
   * 执行数据库备份
   */
  async backup(): Promise<string> {
    this.ensureInitialized();
    
    try {
      // 这里可以实现数据库备份逻辑
      // 例如导出为 SQL 文件或 JSON 格式
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupName = `prompt-snap-backup-${timestamp}`;
      
      // TODO: 实现实际的备份逻辑
      console.log(`创建备份: ${backupName}`);
      
      return backupName;
    } catch (error) {
      throw new Error(`数据库备份失败: ${error}`);
    }
  }

  /**
   * 从备份恢复数据库
   */
  async restore(backupPath: string): Promise<void> {
    this.ensureInitialized();
    
    try {
      // TODO: 实现从备份恢复的逻辑
      console.log(`从备份恢复: ${backupPath}`);
    } catch (error) {
      throw new Error(`数据库恢复失败: ${error}`);
    }
  }

  /**
   * 清理软删除的数据
   */
  async cleanup(olderThanDays: number = 30): Promise<{
    profilesDeleted: number;
    scenesDeleted: number;
    promptsDeleted: number;
    optimizationsDeleted: number;
  }> {
    this.ensureInitialized();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      await this.beginTransaction();

      const results = await Promise.all([
        this.db!.execute(
          'DELETE FROM profiles WHERE is_deleted = TRUE AND updated_at < ?',
          [cutoffISO]
        ),
        this.db!.execute(
          'DELETE FROM scenes WHERE is_deleted = TRUE AND updated_at < ?',
          [cutoffISO]
        ),
        this.db!.execute(
          'DELETE FROM prompts WHERE is_deleted = TRUE AND updated_at < ?',
          [cutoffISO]
        ),
        this.db!.execute(
          'DELETE FROM optimizations WHERE is_deleted = TRUE AND updated_at < ?',
          [cutoffISO]
        ),
      ]);

      await this.commitTransaction();

      return {
        profilesDeleted: results[0].rowsAffected,
        scenesDeleted: results[1].rowsAffected,
        promptsDeleted: results[2].rowsAffected,
        optimizationsDeleted: results[3].rowsAffected,
      };
    } catch (error) {
      await this.rollbackTransaction();
      throw new Error(`数据清理失败: ${error}`);
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    profiles: { total: number; active: number };
    scenes: { total: number; builtIn: number };
    prompts: { total: number; favorites: number };
    optimizations: { total: number; thisMonth: number };
  }> {
    this.ensureInitialized();

    try {
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const [profileStats, sceneStats, promptStats, optimizationStats] = await Promise.all([
        this.db!.select<any[]>(`
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active
          FROM profiles WHERE is_deleted = FALSE
        `),
        this.db!.select<any[]>(`
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_built_in = TRUE THEN 1 ELSE 0 END) as built_in
          FROM scenes WHERE is_deleted = FALSE
        `),
        this.db!.select<any[]>(`
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_favorite = TRUE THEN 1 ELSE 0 END) as favorites
          FROM prompts WHERE is_deleted = FALSE
        `),
        this.db!.select<any[]>(`
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN timestamp >= ? THEN 1 ELSE 0 END) as this_month
          FROM optimizations WHERE is_deleted = FALSE
        `, [thisMonth.toISOString()]),
      ]);

      return {
        profiles: {
          total: profileStats[0].total,
          active: profileStats[0].active,
        },
        scenes: {
          total: sceneStats[0].total,
          builtIn: sceneStats[0].built_in,
        },
        prompts: {
          total: promptStats[0].total,
          favorites: promptStats[0].favorites,
        },
        optimizations: {
          total: optimizationStats[0].total,
          thisMonth: optimizationStats[0].this_month,
        },
      };
    } catch (error) {
      throw new Error(`获取统计信息失败: ${error}`);
    }
  }
}

// 导出单例实例
export const dal = new DataAccessLayer();

// 导出类型和接口
export type { SearchParams, SearchResult } from './prompts';
export type { InitializationOptions } from './initializer';
export type { StartupOptions, StartupResult } from './startup';
export * from './base';
export * from './profiles';
export * from './scenes';
export * from './prompts';
export * from './optimizations';
export * from './settings';
export * from './migration';
export * from './initializer';
export * from './startup';
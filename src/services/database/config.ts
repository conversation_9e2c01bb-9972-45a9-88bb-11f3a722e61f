/**
 * 数据库配置和常量
 */

// 数据库配置
export const DATABASE_CONFIG = {
  // 数据库文件名
  filename: 'prompt_snap.db',
  
  // 连接配置
  connection: {
    // WAL 模式提供更好的并发性能
    journalMode: 'WAL',
    // 正常同步模式平衡性能和安全性
    synchronous: 'NORMAL',
    // 缓存大小 (页数)
    cacheSize: 10000,
    // 临时存储在内存中
    tempStore: 'memory',
    // 启用外键约束
    foreignKeys: true,
  },
  
  // 查询配置
  query: {
    // 默认分页大小
    defaultPageSize: 20,
    // 最大分页大小
    maxPageSize: 100,
    // 搜索结果限制
    searchLimit: 50,
    // 热门标签数量限制
    popularTagsLimit: 20,
  },
  
  // 清理配置
  cleanup: {
    // 默认清理天数
    defaultDays: 30,
    // 最小清理天数
    minDays: 7,
    // 最大清理天数
    maxDays: 365,
  },
} as const;

// 表名常量
export const TABLE_NAMES = {
  PROFILES: 'profiles',
  SCENES: 'scenes',
  PROMPTS: 'prompts',
  OPTIMIZATIONS: 'optimizations',
  SETTINGS: 'settings',
  PROMPTS_FTS: 'prompts_fts',
  OPTIMIZATIONS_FTS: 'optimizations_fts',
} as const;

// 索引名常量
export const INDEX_NAMES = {
  PROFILES_ACTIVE: 'idx_profiles_active',
  PROFILES_CREATED: 'idx_profiles_created',
  PROFILES_NAME: 'idx_profiles_name',
  SCENES_CATEGORY: 'idx_scenes_category',
  SCENES_BUILT_IN: 'idx_scenes_built_in',
  PROMPTS_FAVORITE: 'idx_prompts_favorite',
  PROMPTS_CREATED: 'idx_prompts_created',
  PROMPTS_USAGE: 'idx_prompts_usage',
  OPTIMIZATIONS_TIMESTAMP_PROFILE: 'idx_optimizations_timestamp_profile',
  OPTIMIZATIONS_MODEL: 'idx_optimizations_model',
} as const;

// 视图名常量
export const VIEW_NAMES = {
  ACTIVE_PROFILES: 'active_profiles',
  FAVORITE_PROMPTS: 'favorite_prompts',
  BUILT_IN_SCENES: 'built_in_scenes',
  RECENT_OPTIMIZATIONS: 'recent_optimizations',
  PROFILE_USAGE_STATS: 'profile_usage_stats',
  SCENE_USAGE_STATS: 'scene_usage_stats',
  DAILY_OPTIMIZATION_STATS: 'daily_optimization_stats',
} as const;

// SQL 查询模板
export const SQL_QUERIES = {
  // 通用查询
  COUNT_ALL: (table: string) => `SELECT COUNT(*) as total FROM ${table} WHERE is_deleted = FALSE`,
  
  // 分页查询
  PAGINATE: (baseQuery: string, limit: number, offset: number) => 
    `${baseQuery} LIMIT ${limit} OFFSET ${offset}`,
  
  // 软删除
  SOFT_DELETE: (table: string) => 
    `UPDATE ${table} SET is_deleted = TRUE, updated_at = ? WHERE id = ? AND is_deleted = FALSE`,
  
  // 恢复软删除
  RESTORE: (table: string) => 
    `UPDATE ${table} SET is_deleted = FALSE, updated_at = ? WHERE id = ? AND is_deleted = TRUE`,
  
  // 硬删除（清理）
  HARD_DELETE: (table: string) => 
    `DELETE FROM ${table} WHERE is_deleted = TRUE AND updated_at < ?`,
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NOT_INITIALIZED: '数据库未初始化，请先调用 initialize() 方法',
  CONNECTION_FAILED: '数据库连接失败',
  SCHEMA_INIT_FAILED: '数据库架构初始化失败',
  TRANSACTION_FAILED: '事务执行失败',
  VALIDATION_FAILED: '数据验证失败',
  NOT_FOUND: '记录不存在',
  ALREADY_EXISTS: '记录已存在',
  CONSTRAINT_VIOLATION: '数据约束违反',
  FOREIGN_KEY_VIOLATION: '外键约束违反',
} as const;

// 默认排序配置
export const DEFAULT_SORT = {
  PROFILES: { field: 'created_at', order: 'DESC' },
  SCENES: { field: 'created_at', order: 'DESC' },
  PROMPTS: { field: 'created_at', order: 'DESC' },
  OPTIMIZATIONS: { field: 'timestamp', order: 'DESC' },
} as const;

// 搜索配置
export const SEARCH_CONFIG = {
  // FTS5 查询操作符
  OPERATORS: {
    AND: 'AND',
    OR: 'OR',
    NOT: 'NOT',
    NEAR: 'NEAR',
    PHRASE: '"',
  },
  
  // 搜索字段权重
  FIELD_WEIGHTS: {
    title: 3.0,
    content: 1.0,
    tags: 2.0,
  },
  
  // 最小搜索长度
  MIN_SEARCH_LENGTH: 2,
  
  // 最大搜索长度
  MAX_SEARCH_LENGTH: 100,
} as const;

// 备份配置
export const BACKUP_CONFIG = {
  // 备份文件前缀
  prefix: 'prompt-snap-backup',
  
  // 备份格式
  format: 'json',
  
  // 压缩选项
  compression: true,
  
  // 包含的表
  tables: [
    TABLE_NAMES.PROFILES,
    TABLE_NAMES.SCENES,
    TABLE_NAMES.PROMPTS,
    TABLE_NAMES.OPTIMIZATIONS,
    TABLE_NAMES.SETTINGS,
  ],
  
  // 排除的字段
  excludeFields: ['is_deleted'],
} as const;

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // 慢查询阈值 (毫秒)
  slowQueryThreshold: 1000,
  
  // 是否启用查询日志
  enableQueryLogging: process.env.NODE_ENV === 'development',
  
  // 是否启用性能监控
  enablePerformanceMonitoring: true,
  
  // 统计信息更新间隔 (毫秒)
  statsUpdateInterval: 60000, // 1分钟
} as const;

// 数据验证配置
export const VALIDATION_CONFIG = {
  // 是否启用严格验证
  strictValidation: true,
  
  // 是否在验证失败时抛出异常
  throwOnValidationError: true,
  
  // 是否记录验证错误
  logValidationErrors: true,
  
  // 最大验证错误数量
  maxValidationErrors: 10,
} as const;
/**
 * 优化历史数据访问层
 */

import { DatabaseBase } from './base';
import { 
  OptimizationSchema,
  type Optimization,
  type CreateOptimization
} from '@/schemas';

export class OptimizationsRepository extends DatabaseBase {
  /**
   * 创建优化记录
   */
  async create(data: CreateOptimization): Promise<Optimization> {
    this.ensureInitialized();

    const optimization: Optimization = {
      ...data,
      id: this.generateId(),
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = OptimizationSchema.parse(optimization);
    
    try {
      await this.db!.execute(
        `INSERT INTO optimizations (id, original, optimized, scene_id, profile_id, prompt_id, model_used, timestamp, metadata, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.original,
          validated.optimized,
          validated.sceneId || null,
          validated.profileId || null,
          validated.promptId || null,
          validated.modelUsed,
          validated.timestamp.toISOString(),
          JSON.stringify(validated.metadata),
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );
      
      return validated;
    } catch (error) {
      throw new Error(`创建优化记录失败: ${error}`);
    }
  }

  /**
   * 获取优化记录
   */
  async getById(id: string): Promise<Optimization | null> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM optimizations WHERE id = ? AND is_deleted = FALSE',
        [id]
      );

      if (result.length === 0) return null;

      return this.mapRowToOptimization(result[0]);
    } catch (error) {
      throw new Error(`获取优化记录失败: ${error}`);
    }
  }

  /**
   * 获取优化历史
   */
  async getHistory(options?: {
    page?: number;
    limit?: number;
    profileId?: string;
    sceneId?: string;
    modelUsed?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<{
    items: Optimization[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    this.ensureInitialized();

    try {
      let query = 'SELECT * FROM optimizations WHERE is_deleted = FALSE';
      let countQuery = 'SELECT COUNT(*) as total FROM optimizations WHERE is_deleted = FALSE';
      const params: any[] = [];

      // 添加筛选条件
      if (options?.profileId) {
        query += ' AND profile_id = ?';
        countQuery += ' AND profile_id = ?';
        params.push(options.profileId);
      }

      if (options?.sceneId) {
        query += ' AND scene_id = ?';
        countQuery += ' AND scene_id = ?';
        params.push(options.sceneId);
      }

      if (options?.modelUsed) {
        query += ' AND model_used = ?';
        countQuery += ' AND model_used = ?';
        params.push(options.modelUsed);
      }

      if (options?.startDate) {
        query += ' AND timestamp >= ?';
        countQuery += ' AND timestamp >= ?';
        params.push(options.startDate.toISOString());
      }

      if (options?.endDate) {
        query += ' AND timestamp <= ?';
        countQuery += ' AND timestamp <= ?';
        params.push(options.endDate.toISOString());
      }

      // 添加排序
      query += ' ORDER BY timestamp DESC';

      // 添加分页
      const paginationParams = [...params];
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        query += ' LIMIT ? OFFSET ?';
        paginationParams.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(query, paginationParams),
        this.db!.select<any[]>(countQuery, params)
      ]);

      const items = result.map(row => this.mapRowToOptimization(row));
      const total = countResult[0].total;
      const page = options?.page || 1;
      const limit = options?.limit || total;

      return {
        items,
        total,
        page,
        limit,
        totalPages: limit > 0 ? Math.ceil(total / limit) : 1,
      };
    } catch (error) {
      throw new Error(`获取优化历史失败: ${error}`);
    }
  }

  /**
   * 搜索优化历史
   */
  async search(query: string, options?: {
    page?: number;
    limit?: number;
  }): Promise<{
    items: Optimization[];
    total: number;
    query: string;
    searchTime: number;
  }> {
    this.ensureInitialized();

    const startTime = Date.now();

    try {
      let searchQuery = `
        SELECT o.*, bm25(optimizations_fts) as score FROM optimizations o
        JOIN optimizations_fts fts ON o.rowid = fts.rowid
        WHERE optimizations_fts MATCH ? AND o.is_deleted = FALSE
        ORDER BY score
      `;
      let countQuery = `
        SELECT COUNT(*) as total FROM optimizations o
        JOIN optimizations_fts fts ON o.rowid = fts.rowid
        WHERE optimizations_fts MATCH ? AND o.is_deleted = FALSE
      `;
      
      const queryParams: any[] = [query];

      // 添加分页
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        searchQuery += ' LIMIT ? OFFSET ?';
        queryParams.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(searchQuery, queryParams),
        this.db!.select<any[]>(countQuery, [query])
      ]);

      const items = result.map(row => this.mapRowToOptimization(row));
      const total = countResult[0].total;
      const searchTime = Date.now() - startTime;

      return {
        items,
        total,
        query,
        searchTime,
      };
    } catch (error) {
      throw new Error(`搜索优化历史失败: ${error}`);
    }
  }

  /**
   * 获取最近的优化记录
   */
  async getRecent(limit: number = 10): Promise<Optimization[]> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM optimizations WHERE is_deleted = FALSE ORDER BY timestamp DESC LIMIT ?',
        [limit]
      );

      return result.map(row => this.mapRowToOptimization(row));
    } catch (error) {
      throw new Error(`获取最近优化记录失败: ${error}`);
    }
  }

  /**
   * 软删除优化记录
   */
  async delete(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      const result = await this.db!.execute(
        'UPDATE optimizations SET is_deleted = TRUE, updated_at = ? WHERE id = ? AND is_deleted = FALSE',
        [new Date().toISOString(), id]
      );
      
      if (result.rowsAffected === 0) {
        throw new Error('优化记录不存在或已被删除');
      }
    } catch (error) {
      throw new Error(`删除优化记录失败: ${error}`);
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(options?: {
    profileId?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<{
    totalOptimizations: number;
    totalTokensUsed: number;
    averageQuality: number;
    averageProcessingTime: number;
    modelUsageStats: Array<{ model: string; count: number }>;
  }> {
    this.ensureInitialized();

    try {
      let whereClause = 'WHERE is_deleted = FALSE';
      const params: any[] = [];

      if (options?.profileId) {
        whereClause += ' AND profile_id = ?';
        params.push(options.profileId);
      }

      if (options?.startDate) {
        whereClause += ' AND timestamp >= ?';
        params.push(options.startDate.toISOString());
      }

      if (options?.endDate) {
        whereClause += ' AND timestamp <= ?';
        params.push(options.endDate.toISOString());
      }

      const [statsResult, modelStatsResult] = await Promise.all([
        this.db!.select<any[]>(`
          SELECT 
            COUNT(*) as total_optimizations,
            SUM(CAST(JSON_EXTRACT(metadata, '$.tokensUsed') AS INTEGER)) as total_tokens,
            AVG(CAST(JSON_EXTRACT(metadata, '$.quality') AS REAL)) as avg_quality,
            AVG(CAST(JSON_EXTRACT(metadata, '$.processingTime') AS REAL)) as avg_processing_time
          FROM optimizations ${whereClause}
        `, params),
        this.db!.select<any[]>(`
          SELECT 
            model_used as model,
            COUNT(*) as count
          FROM optimizations ${whereClause}
          GROUP BY model_used
          ORDER BY count DESC
        `, params)
      ]);

      const stats = statsResult[0];

      return {
        totalOptimizations: stats.total_optimizations || 0,
        totalTokensUsed: stats.total_tokens || 0,
        averageQuality: stats.avg_quality || 0,
        averageProcessingTime: stats.avg_processing_time || 0,
        modelUsageStats: modelStatsResult.map(row => ({
          model: row.model,
          count: row.count,
        })),
      };
    } catch (error) {
      throw new Error(`获取统计信息失败: ${error}`);
    }
  }

  /**
   * 将数据库行映射为 Optimization 对象
   */
  private mapRowToOptimization(row: any): Optimization {
    try {
      return OptimizationSchema.parse({
        id: row.id,
        original: row.original,
        optimized: row.optimized,
        sceneId: row.scene_id || undefined,
        profileId: row.profile_id || undefined,
        promptId: row.prompt_id || undefined,
        modelUsed: row.model_used,
        timestamp: new Date(row.timestamp),
        metadata: JSON.parse(row.metadata),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      });
    } catch (error) {
      throw new Error(`数据格式错误：优化历史 - ${error}`);
    }
  }
}
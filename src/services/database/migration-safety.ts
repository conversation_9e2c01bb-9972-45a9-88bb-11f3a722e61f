/**
 * 迁移安全检查器
 * 确保数据库迁移的安全性和一致性
 */

import type { Migration } from './migration';

export interface SafetyCheck {
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  check: (migration: Migration) => boolean;
  message: string;
}

export interface SafetyReport {
  migration: Migration;
  passed: boolean;
  errors: SafetyCheck[];
  warnings: SafetyCheck[];
  infos: SafetyCheck[];
}

export class MigrationSafetyChecker {
  private checks: SafetyCheck[] = [];

  constructor() {
    this.registerChecks();
  }

  /**
   * 注册所有安全检查
   */
  private registerChecks(): void {
    // 错误级别检查
    this.checks.push({
      name: 'no_drop_table_without_backup',
      description: '检查是否有删除表操作但没有备份机制',
      severity: 'error',
      check: (migration) => {
        const hasDropTable = migration.up.some(sql => 
          sql.toUpperCase().includes('DROP TABLE')
        );
        const hasBackupMechanism = migration.up.some(sql => 
          sql.toUpperCase().includes('CREATE TABLE') && sql.toUpperCase().includes('_backup')
        );
        return !hasDropTable || hasBackupMechanism;
      },
      message: '删除表操作应该包含备份机制',
    });

    this.checks.push({
      name: 'no_alter_column_type',
      description: '检查是否有改变列类型的操作（SQLite 不支持）',
      severity: 'error',
      check: (migration) => {
        return !migration.up.some(sql => {
          const upperSql = sql.toUpperCase();
          return upperSql.includes('ALTER TABLE') && 
                 upperSql.includes('ALTER COLUMN') &&
                 (upperSql.includes('TYPE') || upperSql.includes('SET DATA TYPE'));
        });
      },
      message: 'SQLite 不支持直接修改列类型，请使用表重建方式',
    });

    this.checks.push({
      name: 'has_rollback_statements',
      description: '检查是否提供了回滚语句',
      severity: 'error',
      check: (migration) => {
        return migration.down && migration.down.length > 0;
      },
      message: '每个迁移都应该提供回滚语句',
    });

    // 警告级别检查
    this.checks.push({
      name: 'no_data_loss_operations',
      description: '检查可能导致数据丢失的操作',
      severity: 'warning',
      check: (migration) => {
        const dangerousOperations = ['DROP COLUMN', 'DROP TABLE', 'TRUNCATE', 'DELETE FROM'];
        return !migration.up.some(sql => 
          dangerousOperations.some(op => sql.toUpperCase().includes(op))
        );
      },
      message: '迁移包含可能导致数据丢失的操作，请确保已备份数据',
    });

    this.checks.push({
      name: 'has_indexes_for_foreign_keys',
      description: '检查外键是否有对应的索引',
      severity: 'warning',
      check: (migration) => {
        const foreignKeys = this.extractForeignKeys(migration.up);
        const indexes = this.extractIndexes(migration.up);
        
        return foreignKeys.every(fk => 
          indexes.some(idx => idx.includes(fk.column))
        );
      },
      message: '外键列应该创建索引以提高查询性能',
    });

    this.checks.push({
      name: 'no_long_running_operations',
      description: '检查可能长时间运行的操作',
      severity: 'warning',
      check: (migration) => {
        const longRunningOps = ['CREATE INDEX', 'ALTER TABLE', 'UPDATE'];
        const hasLongRunning = migration.up.some(sql => 
          longRunningOps.some(op => sql.toUpperCase().includes(op))
        );
        
        // 如果有长时间运行的操作，检查是否有适当的注释说明
        if (hasLongRunning) {
          return migration.description.includes('长时间') || 
                 migration.description.includes('耗时') ||
                 migration.up.some(sql => sql.includes('--'));
        }
        
        return true;
      },
      message: '长时间运行的操作应该在描述中说明预期耗时',
    });

    // 信息级别检查
    this.checks.push({
      name: 'has_proper_naming',
      description: '检查命名规范',
      severity: 'info',
      check: (migration) => {
        const hasProperTableNames = migration.up.every(sql => {
          if (sql.toUpperCase().includes('CREATE TABLE')) {
            // 检查表名是否使用下划线命名
            const match = sql.match(/CREATE TABLE\s+(\w+)/i);
            if (match) {
              const tableName = match[1];
              return tableName.includes('_') || tableName.toLowerCase() === tableName;
            }
          }
          return true;
        });
        
        return hasProperTableNames;
      },
      message: '建议使用下划线命名法（snake_case）命名表和列',
    });

    this.checks.push({
      name: 'has_comments',
      description: '检查是否有适当的注释',
      severity: 'info',
      check: (migration) => {
        const hasComments = migration.up.some(sql => sql.includes('--'));
        const hasDescription = Boolean(migration.description && migration.description.length > 10);
        return hasComments || hasDescription;
      },
      message: '建议为复杂的迁移添加注释说明',
    });
  }

  /**
   * 检查单个迁移的安全性
   */
  checkMigration(migration: Migration): SafetyReport {
    const errors: SafetyCheck[] = [];
    const warnings: SafetyCheck[] = [];
    const infos: SafetyCheck[] = [];

    for (const check of this.checks) {
      try {
        const passed = check.check(migration);
        
        if (!passed) {
          switch (check.severity) {
            case 'error':
              errors.push(check);
              break;
            case 'warning':
              warnings.push(check);
              break;
            case 'info':
              infos.push(check);
              break;
            default:
              break;
          }
        }
      } catch (error) {
        console.warn(`安全检查 ${check.name} 执行失败:`, error);
      }
    }

    return {
      migration,
      passed: errors.length === 0,
      errors,
      warnings,
      infos,
    };
  }

  /**
   * 检查多个迁移的安全性
   */
  checkMigrations(migrations: Migration[]): SafetyReport[] {
    return migrations.map(migration => this.checkMigration(migration));
  }

  /**
   * 生成安全报告
   */
  generateReport(reports: SafetyReport[]): string {
    let report = '=== 迁移安全检查报告 ===\n\n';

    for (const migrationReport of reports) {
      report += `迁移 ${migrationReport.migration.version}: ${migrationReport.migration.name}\n`;
      report += `状态: ${migrationReport.passed ? '✅ 通过' : '❌ 失败'}\n`;

      if (migrationReport.errors.length > 0) {
        report += '\n错误:\n';
        for (const error of migrationReport.errors) {
          report += `  ❌ ${error.message}\n`;
        }
      }

      if (migrationReport.warnings.length > 0) {
        report += '\n警告:\n';
        for (const warning of migrationReport.warnings) {
          report += `  ⚠️  ${warning.message}\n`;
        }
      }

      if (migrationReport.infos.length > 0) {
        report += '\n建议:\n';
        for (const info of migrationReport.infos) {
          report += `  💡 ${info.message}\n`;
        }
      }

      report += '\n' + '='.repeat(50) + '\n\n';
    }

    const totalMigrations = reports.length;
    const passedMigrations = reports.filter(r => r.passed).length;
    const totalErrors = reports.reduce((sum, r) => sum + r.errors.length, 0);
    const totalWarnings = reports.reduce((sum, r) => sum + r.warnings.length, 0);

    report += '总结:\n';
    report += `  总迁移数: ${totalMigrations}\n`;
    report += `  通过数: ${passedMigrations}\n`;
    report += `  失败数: ${totalMigrations - passedMigrations}\n`;
    report += `  总错误数: ${totalErrors}\n`;
    report += `  总警告数: ${totalWarnings}\n`;

    return report;
  }

  /**
   * 提取外键信息
   */
  private extractForeignKeys(statements: string[]): Array<{ table: string; column: string; refTable: string }> {
    const foreignKeys: Array<{ table: string; column: string; refTable: string }> = [];

    for (const sql of statements) {
      const upperSql = sql.toUpperCase();
      
      // 匹配 FOREIGN KEY 约束
      const fkRegex = /FOREIGN KEY\s*\(\s*(\w+)\s*\)\s*REFERENCES\s+(\w+)/gi;
      let match: RegExpExecArray | null;
      
      match = fkRegex.exec(upperSql);
      while (match !== null) {
        // 提取表名
        const tableMatch = sql.match(/CREATE TABLE\s+(\w+)/i);
        if (tableMatch) {
          foreignKeys.push({
            table: tableMatch[1],
            column: match[1],
            refTable: match[2],
          });
        }
        match = fkRegex.exec(upperSql);
      }
    }

    return foreignKeys;
  }

  /**
   * 提取索引信息
   */
  private extractIndexes(statements: string[]): string[] {
    const indexes: string[] = [];

    for (const sql of statements) {
      if (sql.toUpperCase().includes('CREATE INDEX')) {
        indexes.push(sql);
      }
    }

    return indexes;
  }

  /**
   * 检查迁移序列的一致性
   */
  checkMigrationSequence(migrations: Migration[]): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    // 检查版本号连续性
    const sortedMigrations = [...migrations].sort((a, b) => a.version - b.version);
    
    for (let i = 0; i < sortedMigrations.length; i++) {
      const expected = i + 1;
      const actual = sortedMigrations[i].version;
      
      if (actual !== expected) {
        errors.push(`版本号不连续: 期望 ${expected}，实际 ${actual}`);
      }
    }

    // 检查名称唯一性
    const names = migrations.map(m => m.name);
    const uniqueNames = new Set(names);
    
    if (names.length !== uniqueNames.size) {
      errors.push('存在重复的迁移名称');
    }

    // 检查依赖关系
    for (let i = 1; i < sortedMigrations.length; i++) {
      const current = sortedMigrations[i];
      const previous = sortedMigrations[i - 1];
      
      // 检查是否有对前一个迁移创建的表的依赖
      const currentTables = this.extractTableNames(current.up);
      const previousTables = this.extractTableNames(previous.up);
      
      // 这里可以添加更复杂的依赖检查逻辑
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 提取表名
   */
  private extractTableNames(statements: string[]): string[] {
    const tables: string[] = [];

    for (const sql of statements) {
      const match = sql.match(/CREATE TABLE\s+(\w+)/i);
      if (match) {
        tables.push(match[1]);
      }
    }

    return tables;
  }

  /**
   * 添加自定义检查
   */
  addCustomCheck(check: SafetyCheck): void {
    this.checks.push(check);
  }

  /**
   * 移除检查
   */
  removeCheck(name: string): void {
    this.checks = this.checks.filter(check => check.name !== name);
  }

  /**
   * 获取所有检查
   */
  getChecks(): SafetyCheck[] {
    return [...this.checks];
  }
}
/**
 * 数据库种子数据
 * 提供默认的画像、场景模板等初始数据
 */

import type { CreateProfile, CreateScene, CreateAppSettings } from '@/schemas';
import { DEFAULT_VALUES } from '@/schemas';

// 默认个人画像
export const DEFAULT_PROFILES: CreateProfile[] = [
  {
    name: '通用助手',
    role: '全能AI助手',
    skills: ['问答', '分析', '创作', '翻译'],
    tone: '友好、专业、耐心',
    isActive: true,
  },
  {
    name: '技术专家',
    role: '软件开发和技术咨询专家',
    skills: ['编程', 'JavaScript', 'TypeScript', 'React', 'Node.js', '系统设计'],
    tone: '技术导向、严谨、详细',
    isActive: false,
  },
  {
    name: '写作助手',
    role: '专业写作和内容创作助手',
    skills: ['写作', '编辑', '文案', '创意写作', '学术写作'],
    tone: '创意、流畅、富有表现力',
    isActive: false,
  },
  {
    name: '学习导师',
    role: '教育和学习指导专家',
    skills: ['教学', '解释', '辅导', '课程设计', '学习方法'],
    tone: '耐心、鼓励、循序渐进',
    isActive: false,
  },
];

// 默认场景模板
export const DEFAULT_SCENES: CreateScene[] = [
  {
    title: '通用优化',
    description: '适用于各种类型提示词的通用优化模板',
    template: `请优化以下提示词，使其更加清晰、具体和有效：

原始提示词：{{original_prompt}}

优化要求：
- 使语言更加准确和具体
- 提供必要的上下文信息
- 确保指令清晰明确
- 优化语言表达和结构

请提供优化后的提示词。`,
    variables: [
      {
        name: 'original_prompt',
        description: '需要优化的原始提示词',
        required: true,
      },
    ],
    category: '通用',
    isBuiltIn: true,
  },
  {
    title: '代码相关',
    description: '专门用于优化编程和代码相关的提示词',
    template: `请优化以下编程相关的提示词：

原始提示词：{{original_prompt}}

编程语言：{{programming_language}}
代码类型：{{code_type}}
重点关注：{{focus_areas}}

优化要求：
- 明确指定编程语言和版本
- 详细描述代码需求和约束
- 包含代码质量要求（性能、可读性、安全性等）
- 提供具体的输入输出示例
- 指定代码风格和最佳实践

请提供优化后的提示词。`,
    variables: [
      {
        name: 'original_prompt',
        description: '需要优化的原始提示词',
        required: true,
      },
      {
        name: 'programming_language',
        description: '编程语言',
        defaultValue: 'JavaScript',
        required: false,
      },
      {
        name: 'code_type',
        description: '代码类型（函数、类、组件等）',
        defaultValue: '函数',
        required: false,
      },
      {
        name: 'focus_areas',
        description: '重点关注的方面',
        defaultValue: '性能、可读性、安全性',
        required: false,
      },
    ],
    category: '编程',
    isBuiltIn: true,
  },
  {
    title: '写作优化',
    description: '专门用于优化写作和内容创作相关的提示词',
    template: `请优化以下写作相关的提示词：

原始提示词：{{original_prompt}}

写作类型：{{writing_type}}
目标受众：{{target_audience}}
写作风格：{{writing_style}}
字数要求：{{word_count}}

优化要求：
- 明确写作目的和目标受众
- 指定写作风格和语调
- 提供结构和格式要求
- 包含具体的内容要点
- 设定合适的字数或篇幅

请提供优化后的提示词。`,
    variables: [
      {
        name: 'original_prompt',
        description: '需要优化的原始提示词',
        required: true,
      },
      {
        name: 'writing_type',
        description: '写作类型（文章、报告、邮件等）',
        defaultValue: '文章',
        required: false,
      },
      {
        name: 'target_audience',
        description: '目标读者群体',
        defaultValue: '一般读者',
        required: false,
      },
      {
        name: 'writing_style',
        description: '写作风格',
        defaultValue: '正式、专业',
        required: false,
      },
      {
        name: 'word_count',
        description: '字数要求',
        defaultValue: '500-800字',
        required: false,
      },
    ],
    category: '写作',
    isBuiltIn: true,
  },
  {
    title: '分析任务',
    description: '用于优化数据分析、问题分析等分析类提示词',
    template: `请优化以下分析相关的提示词：

原始提示词：{{original_prompt}}

分析类型：{{analysis_type}}
数据来源：{{data_source}}
分析维度：{{analysis_dimensions}}
输出格式：{{output_format}}

优化要求：
- 明确分析的目标和范围
- 指定分析方法和框架
- 详细描述数据处理要求
- 定义输出格式和结构
- 包含关键指标和评估标准

请提供优化后的提示词。`,
    variables: [
      {
        name: 'original_prompt',
        description: '需要优化的原始提示词',
        required: true,
      },
      {
        name: 'analysis_type',
        description: '分析类型（数据分析、市场分析、问题分析等）',
        defaultValue: '数据分析',
        required: false,
      },
      {
        name: 'data_source',
        description: '数据来源和类型',
        defaultValue: '结构化数据',
        required: false,
      },
      {
        name: 'analysis_dimensions',
        description: '分析维度和角度',
        defaultValue: '趋势、对比、关联',
        required: false,
      },
      {
        name: 'output_format',
        description: '期望的输出格式',
        defaultValue: '报告格式',
        required: false,
      },
    ],
    category: '分析',
    isBuiltIn: true,
  },
  {
    title: '创意生成',
    description: '用于优化创意、设计、头脑风暴等创造性提示词',
    template: `请优化以下创意相关的提示词：

原始提示词：{{original_prompt}}

创意类型：{{creative_type}}
创意主题：{{theme}}
目标用途：{{purpose}}
创意约束：{{constraints}}

优化要求：
- 激发创造性思维和想象力
- 提供具体的创意方向和灵感
- 设定合适的创意约束和边界
- 鼓励多样化和原创性
- 包含评估创意质量的标准

请提供优化后的提示词。`,
    variables: [
      {
        name: 'original_prompt',
        description: '需要优化的原始提示词',
        required: true,
      },
      {
        name: 'creative_type',
        description: '创意类型（文案、设计、概念等）',
        defaultValue: '概念创意',
        required: false,
      },
      {
        name: 'theme',
        description: '创意主题或领域',
        defaultValue: '不限',
        required: false,
      },
      {
        name: 'purpose',
        description: '创意的目标用途',
        defaultValue: '商业应用',
        required: false,
      },
      {
        name: 'constraints',
        description: '创意约束条件',
        defaultValue: '实用性、可行性',
        required: false,
      },
    ],
    category: '创意',
    isBuiltIn: true,
  },
];

// 默认应用设置
export const DEFAULT_APP_SETTINGS: CreateAppSettings = {
  theme: DEFAULT_VALUES.THEME,
  language: DEFAULT_VALUES.LANGUAGE,
  shortcuts: DEFAULT_VALUES.SHORTCUTS,
  aiConfig: {
    model: DEFAULT_VALUES.AI_MODEL,
    temperature: DEFAULT_VALUES.AI_TEMPERATURE,
    maxTokens: DEFAULT_VALUES.AI_MAX_TOKENS,
  },
  autoSave: DEFAULT_VALUES.AUTO_SAVE,
  backupEnabled: DEFAULT_VALUES.BACKUP_ENABLED,
};

// 种子数据插入函数
export class DatabaseSeeder {
  constructor(private dal: any) {}

  /**
   * 插入所有种子数据
   */
  async seedAll(): Promise<void> {
    console.log('开始插入种子数据...');
    
    try {
      await this.seedProfiles();
      await this.seedScenes();
      await this.seedSettings();
      
      console.log('种子数据插入完成');
    } catch (error) {
      console.error('种子数据插入失败:', error);
      throw error;
    }
  }

  /**
   * 插入默认画像
   */
  async seedProfiles(): Promise<void> {
    console.log('插入默认画像...');
    
    for (const profileData of DEFAULT_PROFILES) {
      try {
        // 检查是否已存在同名画像
        const existing = await this.dal.profiles.getAll({ limit: 1000 });
        const existingProfile = existing.items.find((p: any) => p.name === profileData.name);
        
        if (!existingProfile) {
          await this.dal.profiles.create(profileData);
          console.log(`创建默认画像: ${profileData.name}`);
        }
      } catch (error) {
        console.warn(`创建画像 ${profileData.name} 失败:`, error);
      }
    }
  }

  /**
   * 插入默认场景模板
   */
  async seedScenes(): Promise<void> {
    console.log('插入默认场景模板...');
    
    for (const sceneData of DEFAULT_SCENES) {
      try {
        // 检查是否已存在同名场景
        const existing = await this.dal.scenes.getAll({ limit: 1000 });
        const existingScene = existing.items.find((s: any) => s.title === sceneData.title);
        
        if (!existingScene) {
          await this.dal.scenes.create(sceneData);
          console.log(`创建默认场景: ${sceneData.title}`);
        }
      } catch (error) {
        console.warn(`创建场景 ${sceneData.title} 失败:`, error);
      }
    }
  }

  /**
   * 插入默认设置
   */
  async seedSettings(): Promise<void> {
    console.log('检查应用设置...');
    
    try {
      // 尝试获取现有设置
      await this.dal.settings.get();
      console.log('应用设置已存在');
    } catch (error) {
      // 如果设置不存在，会自动创建默认设置
      console.log('创建默认应用设置');
    }
  }

  /**
   * 清除所有种子数据（仅用于测试）
   */
  async clearSeedData(): Promise<void> {
    console.log('清除种子数据...');
    
    try {
      // 删除默认画像
      const profiles = await this.dal.profiles.getAll({ limit: 1000 });
      for (const profile of profiles.items) {
        if (DEFAULT_PROFILES.some(p => p.name === profile.name)) {
          await this.dal.profiles.delete(profile.id);
        }
      }
      
      // 删除默认场景
      const scenes = await this.dal.scenes.getAll({ limit: 1000 });
      for (const scene of scenes.items) {
        if (DEFAULT_SCENES.some(s => s.title === scene.title)) {
          await this.dal.scenes.delete(scene.id);
        }
      }
      
      console.log('种子数据清除完成');
    } catch (error) {
      console.error('清除种子数据失败:', error);
      throw error;
    }
  }
}
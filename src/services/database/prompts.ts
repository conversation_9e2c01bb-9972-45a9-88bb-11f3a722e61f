/**
 * 提示词数据访问层
 */

import { DatabaseBase } from './base';
import { 
  PromptSchema,
  type Prompt,
  type CreatePrompt,
  type UpdatePrompt
} from '@/schemas';

export interface SearchParams {
  query: string;
  tags?: string[];
  profileId?: string;
  sceneId?: string;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  searchTime: number;
}

export class PromptsRepository extends DatabaseBase {
  /**
   * 创建提示词
   */
  async create(data: CreatePrompt): Promise<Prompt> {
    this.ensureInitialized();

    const prompt: Prompt = {
      ...data,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = PromptSchema.parse(prompt);
    
    try {
      await this.db!.execute(
        `INSERT INTO prompts (id, title, content, tags, scene_id, profile_id, is_favorite, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.title,
          validated.content,
          JSON.stringify(validated.tags),
          validated.sceneId || null,
          validated.profileId || null,
          validated.isFavorite,
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );
      
      return validated;
    } catch (error) {
      throw new Error(`创建提示词失败: ${error}`);
    }
  }

  /**
   * 获取提示词
   */
  async getById(id: string): Promise<Prompt | null> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(
        'SELECT * FROM prompts WHERE id = ? AND is_deleted = FALSE',
        [id]
      );

      if (result.length === 0) return null;

      return this.mapRowToPrompt(result[0]);
    } catch (error) {
      throw new Error(`获取提示词失败: ${error}`);
    }
  }

  /**
   * 获取所有提示词
   */
  async getAll(options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    profileId?: string;
    sceneId?: string;
    isFavorite?: boolean;
  }): Promise<{
    items: Prompt[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    this.ensureInitialized();

    try {
      let query = 'SELECT * FROM prompts WHERE is_deleted = FALSE';
      let countQuery = 'SELECT COUNT(*) as total FROM prompts WHERE is_deleted = FALSE';
      const params: any[] = [];

      // 添加筛选条件
      if (options?.profileId) {
        query += ' AND profile_id = ?';
        countQuery += ' AND profile_id = ?';
        params.push(options.profileId);
      }

      if (options?.sceneId) {
        query += ' AND scene_id = ?';
        countQuery += ' AND scene_id = ?';
        params.push(options.sceneId);
      }

      if (options?.isFavorite !== undefined) {
        query += ' AND is_favorite = ?';
        countQuery += ' AND is_favorite = ?';
        params.push(options.isFavorite);
      }

      // 添加排序
      if (options?.sortBy) {
        const sortOrder = options.sortOrder || 'asc';
        query += ` ORDER BY ${options.sortBy} ${sortOrder}`;
      } else {
        query += ' ORDER BY created_at DESC';
      }

      // 添加分页
      const paginationParams = [...params];
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        query += ' LIMIT ? OFFSET ?';
        paginationParams.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(query, paginationParams),
        this.db!.select<any[]>(countQuery, params)
      ]);

      const items = result.map(row => this.mapRowToPrompt(row));
      const total = countResult[0].total;
      const page = options?.page || 1;
      const limit = options?.limit || total;

      return {
        items,
        total,
        page,
        limit,
        totalPages: limit > 0 ? Math.ceil(total / limit) : 1,
      };
    } catch (error) {
      throw new Error(`获取提示词列表失败: ${error}`);
    }
  }

  /**
   * 搜索提示词
   */
  async search(params: SearchParams, options?: {
    page?: number;
    limit?: number;
  }): Promise<SearchResult<Prompt>> {
    this.ensureInitialized();

    const startTime = Date.now();

    try {
      let query = `
        SELECT p.*, bm25(prompts_fts) as score FROM prompts p
        JOIN prompts_fts fts ON p.rowid = fts.rowid
        WHERE prompts_fts MATCH ? AND p.is_deleted = FALSE
      `;
      let countQuery = `
        SELECT COUNT(*) as total FROM prompts p
        JOIN prompts_fts fts ON p.rowid = fts.rowid
        WHERE prompts_fts MATCH ? AND p.is_deleted = FALSE
      `;
      
      const queryParams: any[] = [params.query];
      const conditions: string[] = [];

      // 添加标签筛选
      if (params.tags && params.tags.length > 0) {
        const tagConditions = params.tags.map(() => 'JSON_EXTRACT(p.tags, \"$\") LIKE ?').join(' OR ');
        conditions.push(`(${tagConditions})`);
        params.tags.forEach(tag => {
          queryParams.push(`%\"${tag}\"%`);
        });
      }

      // 添加画像筛选
      if (params.profileId) {
        conditions.push('p.profile_id = ?');
        queryParams.push(params.profileId);
      }

      // 添加场景筛选
      if (params.sceneId) {
        conditions.push('p.scene_id = ?');
        queryParams.push(params.sceneId);
      }

      // 添加额外条件
      if (conditions.length > 0) {
        const additionalConditions = ' AND ' + conditions.join(' AND ');
        query += additionalConditions;
        countQuery += additionalConditions;
      }

      // 添加排序
      query += ' ORDER BY score';

      // 添加分页
      const paginationParams = [...queryParams];
      if (options?.page && options?.limit) {
        const offset = (options.page - 1) * options.limit;
        query += ' LIMIT ? OFFSET ?';
        paginationParams.push(options.limit, offset);
      }

      const [result, countResult] = await Promise.all([
        this.db!.select<any[]>(query, paginationParams),
        this.db!.select<any[]>(countQuery, queryParams)
      ]);

      const items = result.map(row => this.mapRowToPrompt(row));
      const total = countResult[0].total;
      const searchTime = Date.now() - startTime;

      return {
        items,
        total,
        query: params.query,
        searchTime,
      };
    } catch (error) {
      throw new Error(`搜索提示词失败: ${error}`);
    }
  }

  /**
   * 获取收藏的提示词
   */
  async getFavorites(options?: {
    page?: number;
    limit?: number;
  }): Promise<{
    items: Prompt[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return this.getAll({
      ...options,
      isFavorite: true,
      sortBy: 'updated_at',
      sortOrder: 'desc',
    });
  }

  /**
   * 更新提示词
   */
  async update(id: string, data: UpdatePrompt): Promise<Prompt> {
    this.ensureInitialized();

    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('提示词不存在');
    }

    const updated = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    const validated = PromptSchema.parse(updated);

    try {
      await this.db!.execute(
        `UPDATE prompts 
         SET title = ?, content = ?, tags = ?, scene_id = ?, profile_id = ?, is_favorite = ?, updated_at = ?
         WHERE id = ?`,
        [
          validated.title,
          validated.content,
          JSON.stringify(validated.tags),
          validated.sceneId || null,
          validated.profileId || null,
          validated.isFavorite,
          validated.updatedAt.toISOString(),
          id,
        ]
      );

      return validated;
    } catch (error) {
      throw new Error(`更新提示词失败: ${error}`);
    }
  }

  /**
   * 切换收藏状态
   */
  async toggleFavorite(id: string): Promise<Prompt> {
    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('提示词不存在');
    }

    return this.update(id, { isFavorite: !existing.isFavorite });
  }

  /**
   * 增加使用次数
   */
  async incrementUsage(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      await this.db!.execute(
        'UPDATE prompts SET usage_count = usage_count + 1, updated_at = ? WHERE id = ? AND is_deleted = FALSE',
        [new Date().toISOString(), id]
      );
    } catch (error) {
      throw new Error(`更新使用次数失败: ${error}`);
    }
  }

  /**
   * 软删除提示词
   */
  async delete(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      const result = await this.db!.execute(
        'UPDATE prompts SET is_deleted = TRUE, updated_at = ? WHERE id = ? AND is_deleted = FALSE',
        [new Date().toISOString(), id]
      );
      
      if (result.rowsAffected === 0) {
        throw new Error('提示词不存在或已被删除');
      }
    } catch (error) {
      throw new Error(`删除提示词失败: ${error}`);
    }
  }

  /**
   * 获取热门标签
   */
  async getPopularTags(limit: number = 20): Promise<Array<{ tag: string; count: number }>> {
    this.ensureInitialized();

    try {
      const result = await this.db!.select<any[]>(`
        SELECT 
          json_each.value as tag,
          COUNT(*) as count
        FROM prompts, json_each(prompts.tags)
        WHERE prompts.is_deleted = FALSE
        GROUP BY json_each.value
        ORDER BY count DESC
        LIMIT ?
      `, [limit]);

      return result.map(row => ({
        tag: row.tag,
        count: row.count,
      }));
    } catch (error) {
      throw new Error(`获取热门标签失败: ${error}`);
    }
  }

  /**
   * 将数据库行映射为 Prompt 对象
   */
  private mapRowToPrompt(row: any): Prompt {
    try {
      return PromptSchema.parse({
        id: row.id,
        title: row.title,
        content: row.content,
        tags: JSON.parse(row.tags || '[]'),
        sceneId: row.scene_id || undefined,
        profileId: row.profile_id || undefined,
        isFavorite: Boolean(row.is_favorite),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      });
    } catch (error) {
      throw new Error(`数据格式错误：提示词 - ${error}`);
    }
  }
}
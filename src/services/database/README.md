# 数据访问层 (DAL) 文档

## 概述

数据访问层提供了对 Prompt-Snap 应用所有数据操作的统一接口。它封装了数据库操作、数据验证、错误处理和数据映射功能。

## 架构设计

```
DataAccessLayer (主入口)
├── ProfilesRepository (个人画像管理)
├── ScenesRepository (场景模板管理)
├── PromptsRepository (提示词管理)
├── OptimizationsRepository (优化历史管理)
└── SettingsRepository (应用设置管理)
```

## 核心特性

- **类型安全**: 使用 Zod Schema 进行数据验证
- **统一错误处理**: 所有操作都有一致的错误处理机制
- **软删除**: 支持数据的软删除和恢复
- **全文搜索**: 基于 SQLite FTS5 的高性能搜索
- **事务支持**: 复杂操作使用数据库事务保证一致性
- **连接管理**: 自动管理数据库连接和初始化

## 使用方法

### 基础使用

```typescript
import { dal } from '@/services/database';

// 初始化数据库连接
await dal.initialize();

// 使用各个仓库
const profiles = await dal.profiles.getAll();
const activeProfile = await dal.profiles.getActive();
```

### 个人画像管理

```typescript
// 创建个人画像
const newProfile = await dal.profiles.create({
  name: '前端开发专家',
  role: '专业的前端开发工程师',
  skills: ['React', 'TypeScript', 'Node.js'],
  tone: '专业、友好、技术导向',
  isActive: false,
});

// 获取所有画像
const { items, total, totalPages } = await dal.profiles.getAll({
  page: 1,
  limit: 10,
  sortBy: 'created_at',
  sortOrder: 'desc',
});

// 设置活跃画像
await dal.profiles.setActive(newProfile.id);

// 更新画像
const updatedProfile = await dal.profiles.update(newProfile.id, {
  skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
});

// 软删除画像
await dal.profiles.delete(newProfile.id);
```

### 场景模板管理

```typescript
// 创建场景模板
const newScene = await dal.scenes.create({
  title: '代码优化模板',
  description: '用于优化代码相关的提示词',
  template: '请优化以下{{language}}代码：\\n{{code}}\\n\\n重点关注：{{focus_areas}}',
  variables: [
    {
      name: 'language',
      description: '编程语言',
      required: true,
    },
    {
      name: 'code',
      description: '要优化的代码',
      required: true,
    },
    {
      name: 'focus_areas',
      description: '优化重点',
      defaultValue: '性能、可读性、安全性',
      required: false,
    },
  ],
  category: '编程',
  isBuiltIn: false,
});

// 按分类获取模板
const codingScenes = await dal.scenes.getByCategory('编程');

// 获取内置模板
const builtInScenes = await dal.scenes.getBuiltIn();

// 获取所有分类
const categories = await dal.scenes.getCategories();
```

### 提示词管理

```typescript
// 创建提示词
const newPrompt = await dal.prompts.create({
  title: 'React 组件优化',
  content: '请帮我优化这个 React 组件的性能和可读性',
  tags: ['React', '性能优化', '组件'],
  sceneId: newScene.id,
  profileId: newProfile.id,
  isFavorite: false,
});

// 全文搜索提示词
const searchResult = await dal.prompts.search({
  query: 'React 优化',
  tags: ['React'],
  profileId: newProfile.id,
}, {
  page: 1,
  limit: 20,
});

// 获取收藏的提示词
const favorites = await dal.prompts.getFavorites({
  page: 1,
  limit: 10,
});

// 切换收藏状态
await dal.prompts.toggleFavorite(newPrompt.id);

// 获取热门标签
const popularTags = await dal.prompts.getPopularTags(10);
```

### 优化历史管理

```typescript
// 创建优化记录
const optimization = await dal.optimizations.create({
  original: '帮我写个函数',
  optimized: '请帮我编写一个具有以下功能的 JavaScript 函数：...',
  sceneId: newScene.id,
  profileId: newProfile.id,
  promptId: newPrompt.id,
  modelUsed: 'gemini-1.5-pro',
  metadata: {
    tokensUsed: 150,
    processingTime: 1200,
    quality: 85,
    modelVersion: '1.5-pro-001',
  },
});

// 获取优化历史
const history = await dal.optimizations.getHistory({
  page: 1,
  limit: 20,
  profileId: newProfile.id,
  startDate: new Date('2024-01-01'),
  endDate: new Date(),
});

// 搜索优化历史
const searchResults = await dal.optimizations.search('React 组件', {
  page: 1,
  limit: 10,
});

// 获取统计信息
const stats = await dal.optimizations.getStats({
  profileId: newProfile.id,
  startDate: new Date('2024-01-01'),
});

// 获取最近的优化记录
const recentOptimizations = await dal.optimizations.getRecent(5);
```

### 应用设置管理

```typescript
// 获取当前设置
const settings = await dal.settings.get();

// 更新设置
const updatedSettings = await dal.settings.update({
  theme: 'dark',
  language: 'en-US',
  aiConfig: {
    model: 'gemini-1.5-flash',
    temperature: 0.5,
    maxTokens: 1500,
  },
});

// 更新特定配置
await dal.settings.updateTheme('light');
await dal.settings.updateAIConfig({
  model: 'gemini-1.5-pro',
  temperature: 0.7,
  maxTokens: 2000,
});

// 重置为默认设置
await dal.settings.reset();
```

### 数据库管理操作

```typescript
// 获取数据库统计信息
const stats = await dal.getStats();
console.log('数据库统计:', stats);

// 清理软删除的数据（删除30天前的软删除记录）
const cleanupResult = await dal.cleanup(30);
console.log('清理结果:', cleanupResult);

// 创建备份
const backupName = await dal.backup();
console.log('备份创建:', backupName);

// 从备份恢复
await dal.restore('/path/to/backup.json');

// 获取连接状态
const status = dal.getConnectionStatus();
console.log('连接状态:', status);

// 关闭数据库连接
await dal.close();
```

## 错误处理

所有数据库操作都会抛出描述性的错误信息：

```typescript
try {
  const profile = await dal.profiles.create(invalidData);
} catch (error) {
  if (error.message.includes('验证失败')) {
    // 处理验证错误
    console.error('数据验证失败:', error.message);
  } else if (error.message.includes('数据库')) {
    // 处理数据库错误
    console.error('数据库操作失败:', error.message);
  }
}
```

## 性能优化

### 分页查询

所有列表查询都支持分页：

```typescript
const result = await dal.prompts.getAll({
  page: 1,
  limit: 20,
  sortBy: 'created_at',
  sortOrder: 'desc',
});

console.log(`第 ${result.page} 页，共 ${result.totalPages} 页`);
console.log(`显示 ${result.items.length} 条，共 ${result.total} 条记录`);
```

### 索引优化

数据库使用了多种索引来优化查询性能：

- 主键索引
- 外键索引
- 复合索引（如时间戳+用户ID）
- 全文搜索索引（FTS5）
- 条件索引（仅索引未删除的记录）

### 查询优化建议

1. **使用分页**: 避免一次性加载大量数据
2. **利用索引**: 在 WHERE 子句中使用已索引的字段
3. **避免 N+1 查询**: 使用 JOIN 或批量查询
4. **使用全文搜索**: 对于文本搜索使用 FTS5 而不是 LIKE

## 数据验证

所有数据在存储前都会通过 Zod Schema 进行验证：

```typescript
// 自动验证
const profile = await dal.profiles.create({
  name: 'Test', // 会验证长度和格式
  skills: ['React'], // 会验证数组长度和元素格式
  // ... 其他字段
});

// 手动验证
import { validateProfile } from '@/schemas';
try {
  const validatedData = validateProfile(rawData);
} catch (error) {
  console.error('验证失败:', error.message);
}
```

## 事务处理

复杂操作使用事务确保数据一致性：

```typescript
// 设置活跃画像（需要先取消其他画像的活跃状态）
await dal.profiles.setActive(profileId);

// 清理操作（批量删除多个表的数据）
await dal.cleanup(30);
```

## 最佳实践

1. **始终初始化**: 在使用前调用 `dal.initialize()`
2. **错误处理**: 使用 try-catch 包装所有数据库操作
3. **资源清理**: 应用退出时调用 `dal.close()`
4. **数据验证**: 依赖内置的 Zod 验证，不要跳过验证
5. **分页查询**: 对于可能返回大量数据的查询使用分页
6. **软删除**: 使用软删除而不是硬删除，保留数据完整性

## 扩展开发

### 添加新的仓库

1. 创建新的仓库类继承 `DatabaseBase`
2. 实现必要的 CRUD 方法
3. 在 `DataAccessLayer` 中添加仓库实例
4. 更新数据库 schema 和索引

### 添加新的查询方法

```typescript
// 在相应的仓库类中添加方法
async getCustomQuery(params: CustomParams): Promise<CustomResult> {
  this.ensureInitialized();
  
  try {
    const result = await this.db!.select<any[]>(
      'SELECT * FROM table WHERE condition = ?',
      [params.value]
    );
    
    return result.map(row => this.mapRowToEntity(row));
  } catch (error) {
    throw new Error(`自定义查询失败: ${error}`);
  }
}
```

这个数据访问层为 Prompt-Snap 应用提供了完整、类型安全、高性能的数据操作接口。
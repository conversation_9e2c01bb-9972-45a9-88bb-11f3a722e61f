/**
 * 数据库初始化管理器
 * 协调数据库迁移、种子数据插入和初始化流程
 */

import { DatabaseMigration } from './migration';
import { DatabaseSeeder } from './seed';
import type { DataAccessLayer } from './index';

export interface InitializationOptions {
  // 是否执行迁移
  runMigrations?: boolean;
  // 是否插入种子数据
  insertSeedData?: boolean;
  // 是否验证数据库完整性
  validateDatabase?: boolean;
  // 是否在开发环境重置数据库
  resetInDevelopment?: boolean;
  // 初始化超时时间（毫秒）
  timeout?: number;
}

export interface InitializationResult {
  success: boolean;
  version: number;
  migrationsApplied: number;
  seedDataInserted: boolean;
  validationResult?: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
  duration: number;
  error?: string;
}

export class DatabaseInitializer {
  private migration: DatabaseMigration;
  private seeder: DatabaseSeeder;
  private dal: DataAccessLayer;

  constructor(dal: DataAccessLayer) {
    this.dal = dal;
    this.migration = new DatabaseMigration();
    this.seeder = new DatabaseSeeder(dal);
  }

  /**
   * 执行完整的数据库初始化流程
   */
  async initialize(options: InitializationOptions = {}): Promise<InitializationResult> {
    const startTime = Date.now();
    const defaultOptions: Required<InitializationOptions> = {
      runMigrations: true,
      insertSeedData: true,
      validateDatabase: true,
      resetInDevelopment: false,
      timeout: 30_000, // 30秒超时
    };

    const config = { ...defaultOptions, ...options };
    
    console.log('开始数据库初始化...');

    try {
      // 设置超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('数据库初始化超时')), config.timeout);
      });

      const initPromise = this.performInitialization(config);
      const result = await Promise.race([initPromise, timeoutPromise]);

      const duration = Date.now() - startTime;
      console.log(`数据库初始化完成，耗时 ${duration}ms`);

      return {
        ...result,
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('数据库初始化失败:', error);

      return {
        success: false,
        version: 0,
        migrationsApplied: 0,
        seedDataInserted: false,
        duration,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 执行初始化流程
   */
  private async performInitialization(config: Required<InitializationOptions>): Promise<InitializationResult> {
    let migrationsApplied = 0;
    let seedDataInserted = false;
    let validationResult: {
      isValid: boolean;
      errors: string[];
      warnings: string[];
    } | undefined;

    // 1. 初始化基础数据库连接
    console.log('1. 初始化数据库连接...');
    await this.dal.initialize(false); // 不自动插入种子数据

    // 2. 初始化迁移系统
    console.log('2. 初始化迁移系统...');
    await this.migration.initialize();

    // 3. 处理开发环境重置
    if (config.resetInDevelopment && process.env.NODE_ENV === 'development') {
      console.log('3. 重置开发环境数据库...');
      await this.migration.resetDatabase();
    }

    // 4. 执行数据库迁移
    if (config.runMigrations) {
      console.log('4. 执行数据库迁移...');
      const beforeVersion = await this.migration.getCurrentVersion();
      await this.migration.migrate();
      const afterVersion = await this.migration.getCurrentVersion();
      migrationsApplied = afterVersion - beforeVersion;
    }

    // 5. 插入种子数据
    if (config.insertSeedData) {
      console.log('5. 插入种子数据...');
      try {
        await this.seeder.seedAll();
        seedDataInserted = true;
      } catch (error) {
        console.warn('种子数据插入失败，但不影响初始化:', error);
        // 种子数据插入失败不应该导致整个初始化失败
      }
    }

    // 6. 验证数据库完整性
    if (config.validateDatabase) {
      console.log('6. 验证数据库完整性...');
      validationResult = await this.migration.validateDatabase();
      
      if (!validationResult.isValid) {
        console.warn('数据库验证发现问题:', validationResult.errors);
        // 根据错误严重程度决定是否继续
        const criticalErrors = validationResult.errors.filter(error => 
          error.includes('缺少必需的表') || error.includes('校验和不匹配')
        );
        
        if (criticalErrors.length > 0) {
          throw new Error(`数据库验证失败: ${criticalErrors.join(', ')}`);
        }
      }
    }

    const currentVersion = await this.migration.getCurrentVersion();

    return {
      success: true,
      version: currentVersion,
      migrationsApplied,
      seedDataInserted,
      validationResult,
      duration: 0, // 将在外层设置
    };
  }

  /**
   * 检查数据库是否需要初始化
   */
  async needsInitialization(): Promise<{
    needsMigration: boolean;
    needsSeedData: boolean;
    currentVersion: number;
    latestVersion: number;
  }> {
    try {
      await this.dal.initialize(false);
      await this.migration.initialize();

      const status = await this.migration.getDatabaseStatus();
      
      // 检查是否需要种子数据
      let needsSeedData = false;
      try {
        const profiles = await this.dal.profiles.getAll({ limit: 1 });
        needsSeedData = profiles.total === 0;
      } catch (error) {
        needsSeedData = true;
      }

      return {
        needsMigration: !status.isUpToDate,
        needsSeedData,
        currentVersion: status.currentVersion,
        latestVersion: status.latestVersion,
      };
    } catch (error) {
      // 如果检查失败，假设需要完整初始化
      return {
        needsMigration: true,
        needsSeedData: true,
        currentVersion: 0,
        latestVersion: 1,
      };
    }
  }

  /**
   * 获取数据库状态
   */
  async getStatus(): Promise<{
    isInitialized: boolean;
    version: number;
    hasSeedData: boolean;
    lastInitialization?: Date;
    validationResult?: {
      isValid: boolean;
      errors: string[];
      warnings: string[];
    };
  }> {
    try {
      await this.dal.initialize(false);
      await this.migration.initialize();

      const status = await this.migration.getDatabaseStatus();
      const validationResult = await this.migration.validateDatabase();

      // 检查种子数据
      let hasSeedData = false;
      try {
        const profiles = await this.dal.profiles.getAll({ limit: 1 });
        hasSeedData = profiles.total > 0;
      } catch (error) {
        hasSeedData = false;
      }

      return {
        isInitialized: status.isUpToDate && validationResult.isValid,
        version: status.currentVersion,
        hasSeedData,
        validationResult,
      };
    } catch (error) {
      return {
        isInitialized: false,
        version: 0,
        hasSeedData: false,
        validationResult: {
          isValid: false,
          errors: [error instanceof Error ? error.message : String(error)],
          warnings: [],
        },
      };
    }
  }

  /**
   * 修复数据库问题
   */
  async repair(): Promise<{
    success: boolean;
    actionsPerformed: string[];
    error?: string;
  }> {
    const actionsPerformed: string[] = [];

    try {
      console.log('开始数据库修复...');

      // 1. 验证当前状态
      const validationResult = await this.migration.validateDatabase();
      
      if (validationResult.isValid) {
        return {
          success: true,
          actionsPerformed: ['数据库状态正常，无需修复'],
        };
      }

      // 2. 尝试重新运行迁移
      if (validationResult.errors.some(error => error.includes('缺少必需的表'))) {
        console.log('检测到缺少表，重新运行迁移...');
        await this.migration.migrate();
        actionsPerformed.push('重新运行数据库迁移');
      }

      // 3. 重新插入种子数据
      try {
        const profiles = await this.dal.profiles.getAll({ limit: 1 });
        if (profiles.total === 0) {
          console.log('检测到缺少种子数据，重新插入...');
          await this.seeder.seedAll();
          actionsPerformed.push('重新插入种子数据');
        }
      } catch (error) {
        console.log('重新插入种子数据...');
        await this.seeder.seedAll();
        actionsPerformed.push('重新插入种子数据');
      }

      // 4. 再次验证
      const finalValidation = await this.migration.validateDatabase();
      
      if (!finalValidation.isValid) {
        throw new Error(`修复后仍有问题: ${finalValidation.errors.join(', ')}`);
      }

      console.log('数据库修复完成');
      return {
        success: true,
        actionsPerformed,
      };
    } catch (error) {
      console.error('数据库修复失败:', error);
      return {
        success: false,
        actionsPerformed,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 创建数据库备份
   */
  async createBackup(): Promise<{
    success: boolean;
    backupPath?: string;
    error?: string;
  }> {
    try {
      const backupName = await this.dal.backup();
      return {
        success: true,
        backupPath: backupName,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 从备份恢复数据库
   */
  async restoreFromBackup(backupPath: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await this.dal.restore(backupPath);
      
      // 恢复后重新验证
      const validationResult = await this.migration.validateDatabase();
      if (!validationResult.isValid) {
        throw new Error(`恢复后验证失败: ${validationResult.errors.join(', ')}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
import React from 'react';
import { 
  SidebarInset, 
  SidebarProvider, 
  SidebarTrigger 
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { AppSidebar } from '@/components/layout/AppShell';
import { PromptOptimizeForm } from '@/components/optimization/PromptOptimizeForm';
import { OptimizeResultDisplay } from '@/components/optimization/OptimizeResultDisplay';
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from '@/components/ui/breadcrumb';
import { useOptimizationStore } from '@/stores';

export function OptimizePage() {
  const currentResult = useOptimizationStore((state) => state.currentResult);

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/">Prompt-Snap</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>提示词优化</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="mx-auto w-full max-w-4xl space-y-6">
            <PromptOptimizeForm />
            
            {currentResult && (
              <OptimizeResultDisplay
                original={currentResult.original}
                optimized={currentResult.optimized}
                qualityScore={currentResult.qualityScore}
                improvements={currentResult.improvements}
              />
            )}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
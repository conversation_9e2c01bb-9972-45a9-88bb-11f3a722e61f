import { useEffect } from 'react';
import { useAppStore } from '@/stores';
import { DatabaseService } from '@/services/database';

export function useDatabase() {
  const database = useAppStore((state) => state.database);
  const setDatabase = useAppStore((state) => state.setDatabase);
  const isInitialized = useAppStore((state) => state.isDatabaseInitialized);
  const setInitialized = useAppStore((state) => state.setDatabaseInitialized);

  useEffect(() => {
    const initializeDatabase = async () => {
      if (isInitialized || database) return;
      
      try {
        const db = new DatabaseService();
        await db.initialize();
        setDatabase(db);
        setInitialized(true);
        
        // 初始化完成后加载相关数据
        const { useProfileStore, useOptimizationStore } = await import('@/stores');
        useProfileStore.getState().loadProfiles();
        useOptimizationStore.getState().loadHistory();
      } catch (error) {
        console.error('数据库初始化失败:', error);
        useAppStore.getState().setError('数据库初始化失败');
      }
    };

    initializeDatabase();
  }, [database, isInitialized, setDatabase, setInitialized]);

  return database;
}
import React from 'react';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function AppLayout({ children, className }: AppLayoutProps) {
  return (
    <div className={cn("flex h-screen bg-background", className)}>
      {children}
    </div>
  );
}

interface SidebarProps {
  children: React.ReactNode;
  className?: string;
}

export function Sidebar({ children, className }: SidebarProps) {
  return (
    <aside className={cn(
      "w-64 bg-card border-r flex flex-col",
      className
    )}>
      {children}
    </aside>
  );
}

interface MainContentProps {
  children: React.ReactNode;
  className?: string;
}

export function MainContent({ children, className }: MainContentProps) {
  return (
    <main className={cn("flex-1 overflow-hidden", className)}>
      {children}
    </main>
  );
}

interface HeaderProps {
  children: React.ReactNode;
  className?: string;
}

export function Header({ children, className }: HeaderProps) {
  return (
    <header className={cn(
      "h-14 border-b bg-card flex items-center px-4",
      className
    )}>
      {children}
    </header>
  );
}
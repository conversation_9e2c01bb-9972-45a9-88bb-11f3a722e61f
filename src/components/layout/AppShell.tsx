import React from 'react';
import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import {
  Home,
  MessageSquare,
  User,
  Settings,
  History,
  Sparkles,
  Bot,
} from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

const items = [
  {
    title: '优化',
    url: '/',
    icon: Bot,
  },
  {
    title: '提示词仓库',
    url: '/prompts',
    icon: MessageSquare,
  },
  {
    title: '个人画像',
    url: '/profiles',
    icon: User,
  },
  {
    title: '场景模板',
    url: '/scenes',
    icon: Sparkles,
  },
  {
    title: '优化历史',
    url: '/history',
    icon: History,
  },
  {
    title: '设置',
    url: '/settings',
    icon: Settings,
  },
];

export function AppSidebar() {
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <Bot className="h-6 w-6 text-primary" />
          <span className="text-lg font-semibold">Prompt-Snap</span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>功能</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    onClick={() => navigate(item.url)}
                    isActive={location.pathname === item.url}
                  >
                    <item.icon />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="p-4">
          <p className="text-xs text-muted-foreground">
            AI 提示词优化工具
          </p>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
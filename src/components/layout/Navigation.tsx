import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, 
  MessageSquare, 
  User, 
  Settings, 
  History, 
  Sparkles 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
  to: string;
  icon: React.ReactNode;
  label: string;
}

const navigation: NavItem[] = [
  {
    to: '/',
    icon: <Home className="h-4 w-4" />,
    label: '优化'
  },
  {
    to: '/profiles',
    icon: <User className="h-4 w-4" />,
    label: '个人画像'
  },
  {
    to: '/scenes',
    icon: <Sparkles className="h-4 w-4" />,
    label: '场景模板'
  },
  {
    to: '/prompts',
    icon: <MessageSquare className="h-4 w-4" />,
    label: '提示词仓库'
  },
  {
    to: '/history',
    icon: <History className="h-4 w-4" />,
    label: '优化历史'
  },
  {
    to: '/settings',
    icon: <Settings className="h-4 w-4" />,
    label: '设置'
  }
];

interface NavigationProps {
  className?: string;
}

export function Navigation({ className }: NavigationProps) {
  return (
    <nav className={cn("flex-1 px-3 py-4", className)}>
      <ul className="space-y-2">
        {navigation.map((item) => (
          <li key={item.to}>
            <NavLink
              to={item.to}
              className={({ isActive }) =>
                cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                  isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground"
                )
              }
            >
              {item.icon}
              {item.label}
            </NavLink>
          </li>
        ))}
      </ul>
    </nav>
  );
}
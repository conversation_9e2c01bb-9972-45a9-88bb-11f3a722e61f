import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Copy, RefreshCw, Save } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OptimizeResultDisplayProps {
  original: string;
  optimized: string;
  improvements?: string[];
  qualityScore?: number;
  onCopy?: () => void;
  onRetry?: () => void;
  onSave?: () => void;
  className?: string;
}

export function OptimizeResultDisplay({
  original,
  optimized,
  improvements = [],
  qualityScore,
  onCopy,
  onRetry,
  onSave,
  className,
}: OptimizeResultDisplayProps) {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(optimized);
      onCopy?.();
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 80) return 'bg-yellow-500';
    return 'bg-orange-500';
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">原始提示词</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64 rounded-md border p-4">
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">{original}</p>
            </ScrollArea>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">优化结果</CardTitle>
            <CardDescription>
              {qualityScore && (
                <Badge className={getQualityColor(qualityScore)}>
                  质量评分: {qualityScore}/100
                </Badge>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64 rounded-md border p-4 bg-muted/50">
              <p className="text-sm whitespace-pre-wrap">{optimized}</p>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {improvements.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">改进点</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {improvements.map((improvement, index) => (
                <li key={index} className="text-sm text-muted-foreground">
                  • {improvement}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      <div className="flex gap-2 justify-end">
        <Button onClick={handleCopy} variant="outline">
          <Copy className="h-4 w-4 mr-2" />
          复制结果
        </Button>
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重新优化
        </Button>
        <Button onClick={onSave}>
          <Save className="h-4 w-4 mr-2" />
          保存结果
        </Button>
      </div>
    </div>
  );
}
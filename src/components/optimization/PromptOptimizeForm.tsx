import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useProfileStore, useOptimizationStore } from '@/stores';
import { useDatabase } from '@/hooks/useDatabase';

const optimizeFormSchema = z.object({
  prompt: z.string().min(1, '请输入需要优化的提示词'),
  profileId: z.string().optional(),
});

type OptimizeFormData = z.infer<typeof optimizeFormSchema>;

interface PromptOptimizeFormProps {
  defaultPrompt?: string;
}

export function PromptOptimizeForm({ defaultPrompt = '' }: PromptOptimizeFormProps) {
  const database = useDatabase();
  const profiles = useProfileStore((state) => state.profiles);
  const isLoading = useProfileStore((state) => state.isLoading);
  const isOptimizing = useOptimizationStore((state) => state.isOptimizing);
  const optimizePrompt = useOptimizationStore((state) => state.optimizePrompt);
  const setCurrentPrompt = useOptimizationStore((state) => state.setCurrentPrompt);

  const form = useForm<OptimizeFormData>({
    resolver: zodResolver(optimizeFormSchema),
    defaultValues: {
      prompt: defaultPrompt,
      profileId: '',
    },
  });

  useEffect(() => {
    useProfileStore.getState().loadProfiles();
  }, []);

  useEffect(() => {
    // 设置默认选中的活跃画像
    const activeProfile = profiles.find(p => p.isActive);
    if (activeProfile) {
      form.setValue('profileId', activeProfile.id);
    }
  }, [profiles, form]);

  const onSubmit = async (data: OptimizeFormData) => {
    if (!database) return;
    
    try {
      const profile = data.profileId 
        ? profiles.find(p => p.id === data.profileId)
        : undefined;
        
      setCurrentPrompt(data.prompt);
      await optimizePrompt(data.prompt, profile);
    } catch (error) {
      console.error('优化失败:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>提示词优化</CardTitle>
        <CardDescription>
          输入您想要优化的提示词，AI 将基于您的个人画像进行智能优化
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="prompt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>提示词内容</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入需要优化的提示词..."
                      className="min-h-[120px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    建议提供完整的上下文和需求说明
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="profileId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>个人画像</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择一个个人画像" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">不使用画像</SelectItem>
                      {profiles.map((profile) => (
                        <SelectItem key={profile.id} value={profile.id}>
                          {profile.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    选择个人画像以获得更个性化的优化结果
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isOptimizing || !database} className="w-full">
              {isOptimizing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  优化中...
                </>
              ) : (
                '开始优化'
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
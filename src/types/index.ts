/**
 * 应用程序类型定义
 * 补充 Zod Schema 之外的类型定义
 */

import type { ReactNode } from 'react';
import type { 
  Theme,
  Scene,
  Profile,
  AppSettings,
  OptimizationMetadata
} from '../schemas';

// 重新导出 Schema 类型
export type {
  Variable,
  Profile,
  Scene,
  Prompt,
  Optimization,
  OptimizationMetadata,
  AppSettings,
  Shortcuts,
  AIConfig,
  CreateProfile,
  CreateScene,
  CreatePrompt,
  CreateOptimization,
  CreateAppSettings,
  UpdateProfile,
  UpdateScene,
  UpdatePrompt,
  UpdateAppSettings,
  ProfileFormData,
  SceneFormData,
  PromptFormData,
  OptimizationFormData,
  AppSettingsFormData,
  Theme,
} from '../schemas';

// 数据库操作相关类型
export interface DatabaseConnection {
  isConnected: boolean;
  lastError?: string;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 搜索相关类型
export interface SearchParams {
  query: string;
  tags?: string[];
  category?: string;
  profileId?: string;
  sceneId?: string;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  searchTime: number;
}

// 优化请求类型
export interface OptimizationRequest {
  original: string;
  profileId: string;
  sceneId?: string;
  variables?: Record<string, string>;
}

export interface OptimizationResult {
  optimized: string;
  metadata: OptimizationMetadata;
  optimizationId?: string;
}

// 错误类型
export const ErrorType = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  AI_SERVICE_ERROR: 'AI_SERVICE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
} as const;

export type ErrorTypeValue = typeof ErrorType[keyof typeof ErrorType];

export class AppError extends Error {
  type: ErrorTypeValue;
  details?: unknown;
  code?: string;

  constructor(
    type: ErrorTypeValue,
    message: string,
    details?: unknown,
    code?: string
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.details = details;
    this.code = code;
  }
}

// 组件状态类型
export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
}

export interface ErrorState {
  hasError: boolean;
  error?: AppError | Error;
  errorMessage?: string;
}

// 表单状态类型
export interface FormState<T = unknown> extends LoadingState, ErrorState {
  data: T;
  isDirty: boolean;
  isValid: boolean;
  validationErrors?: Record<string, string>;
}

// 列表状态类型
export interface ListState<T = unknown> extends LoadingState, ErrorState {
  items: T[];
  selectedItems: string[];
  filters: Record<string, unknown>;
  pagination?: PaginationParams;
  total: number;
}

// 模态框状态类型
export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
}

// 通知类型
export const NotificationType = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

export type NotificationTypeValue = typeof NotificationType[keyof typeof NotificationType];

export interface Notification {
  id: string;
  type: NotificationTypeValue;
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 排序类型
export type SortOrder = 'asc' | 'desc';

// 文件类型
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

// 导出导入类型
export interface ExportOptions {
  format: 'json' | 'csv' | 'txt';
  includeMetadata: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
}

// 统计类型
export interface Statistics {
  totalPrompts: number;
  totalOptimizations: number;
  totalProfiles: number;
  totalScenes: number;
  averageQuality: number;
  mostUsedProfile?: Profile;
  mostUsedScene?: Scene;
  recentActivity: Array<{
    type: 'create' | 'update' | 'optimize';
    timestamp: Date;
    description: string;
  }>;
}

// 系统信息类型
export interface SystemInfo {
  version: string;
  platform: string;
  databaseSize: number;
  lastBackup?: Date;
  uptime: number;
}

// 快捷键事件类型
export interface ShortcutEvent {
  key: string;
  action: string;
  timestamp: Date;
}

// 剪贴板操作类型
export interface ClipboardOperation {
  type: 'read' | 'write';
  content?: string;
  success: boolean;
  timestamp: Date;
}

// 全局状态类型（用于 Context）
export interface GlobalState {
  // 用户设置
  settings: AppSettings;
  
  // 当前活跃的画像和场景
  activeProfile?: Profile;
  activeScene?: Scene;
  
  // UI 状态
  theme: Theme;
  language: Language;
  sidebarCollapsed: boolean;
  
  // 通知
  notifications: Notification[];
  
  // 模态框
  modal: ModalState;
  
  // 系统状态
  isOnline: boolean;
  systemInfo?: SystemInfo;
}

// Action 类型（用于 useReducer）
export type GlobalAction =
  | { type: 'SET_SETTINGS'; payload: AppSettings }
  | { type: 'SET_ACTIVE_PROFILE'; payload: Profile | undefined }
  | { type: 'SET_ACTIVE_SCENE'; payload: Scene | undefined }
  | { type: 'SET_THEME'; payload: Theme }
  | { type: 'SET_LANGUAGE'; payload: Language }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'SHOW_MODAL'; payload: Omit<ModalState, 'isOpen'> }
  | { type: 'HIDE_MODAL' }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_SYSTEM_INFO'; payload: SystemInfo };
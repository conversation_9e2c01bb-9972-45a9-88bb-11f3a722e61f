# Prompt-Snap 需求文档

## 项目简介

Prompt-Snap 是一个智能提示词优化工具，旨在帮助用户快速优化和管理提示词。用户可以通过自定义快捷键快速抓取当前输入框内容，基于个人画像和场景模板进行AI优化，并将结果写回。同时提供完整的提示词仓库、优化历史记录和模板管理功能。

初期将开发网页端版本，后续扩展为桌面应用。

## 需求列表

### 需求 1：提示词仓库管理

**用户故事：** 作为用户，我希望能够管理我的提示词库，以便我可以保存、搜索和重用常用的提示词。

#### 验收标准

1. 当用户访问提示词仓库页面时，系统应显示所有已保存的提示词列表
2. 当用户点击"新建提示词"按钮时，系统应提供表单让用户输入标题、内容和标签
3. 当用户在搜索框输入关键词时，系统应实时过滤显示匹配的提示词
4. 当用户选择标签筛选时，系统应只显示包含该标签的提示词
5. 当用户点击编辑按钮时，系统应允许修改提示词的所有属性
6. 当用户点击删除按钮时，系统应显示确认对话框并在确认后删除提示词
7. 当用户点击导出按钮时，系统应将选中的提示词导出为JSON文件
8. 当用户上传导入文件时，系统应解析并添加新的提示词到仓库

### 需求 2：AI提示词优化

**用户故事：** 作为用户，我希望能够使用AI优化我的提示词，以便获得更好的AI响应效果。

#### 验收标准

1. 当用户在文本框输入原始提示词时，系统应保存该内容
2. 当用户选择个人画像和场景模板时，系统应记录当前选择
3. 当用户点击"优化"按钮时，系统应调用Google Gemini API进行提示词优化
4. 当优化完成时，系统应显示优化后的提示词内容
5. 当优化过程中出现错误时，系统应显示友好的错误提示
6. 当优化成功时，系统应自动将结果复制到剪贴板
7. 当用户设置为直接替换模式时，系统应直接用优化结果替换原文本
8. 当优化完成时，系统应自动保存一条优化历史记录

### 需求 3：个人画像管理

**用户故事：** 作为用户，我希望能够创建和管理多个个人画像，以便针对不同角色进行个性化的提示词优化。

#### 验收标准

1. 当用户访问设置页面时，系统应显示当前所有个人画像
2. 当用户点击"新建画像"时，系统应提供表单输入姓名、角色、技能和语调
3. 当用户编辑画像信息时，系统应保存所有修改
4. 当用户删除画像时，系统应显示确认对话框并处理相关依赖
5. 当用户在优化页面时，系统应显示画像选择下拉菜单
6. 当用户切换画像时，系统应更新当前活跃画像状态
7. 当系统进行优化时，系统应基于选中画像的属性调整优化策略

### 需求 4：场景模板管理

**用户故事：** 作为用户，我希望能够创建和使用场景模板，以便快速应用特定场景的优化规则。

#### 验收标准

1. 当用户访问模板页面时，系统应显示所有可用的场景模板
2. 当用户创建新模板时，系统应支持添加变量占位符（如{{变量名}}）
3. 当用户编辑模板时，系统应提供变量预览功能
4. 当用户在优化时选择模板时，系统应将模板规则应用到优化过程
5. 当用户点击"保存为模板"时，系统应基于当前优化结果创建新模板
6. 当模板包含变量时，系统应在使用前提示用户填入变量值
7. 当用户删除模板时，系统应检查依赖关系并给出适当提示

### 需求 5：优化历史记录

**用户故事：** 作为用户，我希望能够查看和管理我的优化历史，以便回顾、对比和重用之前的优化结果。

#### 验收标准

1. 当用户访问历史页面时，系统应按时间倒序显示所有优化记录
2. 当用户点击某条历史记录时，系统应显示原文和优化结果的对比视图
3. 当用户点击"回滚"按钮时，系统应将原文复制到剪贴板
4. 当用户点击"重新优化"按钮时，系统应使用相同参数重新进行优化
5. 当用户点击"保存为模板"时，系统应基于该历史记录创建场景模板
6. 当用户搜索历史时，系统应支持按内容、日期、画像等条件筛选
7. 当历史记录过多时，系统应提供分页或虚拟滚动功能

### 需求 6：快捷键和剪贴板集成

**用户故事：** 作为用户，我希望能够通过快捷键快速触发优化功能，以便提高工作效率。

#### 验收标准

1. 当用户在设置中配置快捷键时，系统应保存用户的自定义设置
2. 当用户按下配置的快捷键时，系统应自动读取当前剪贴板内容
3. 当检测到剪贴板有文本内容时，系统应自动填入优化输入框
4. 当优化完成时，系统应根据用户设置决定是否自动写回剪贴板
5. 当用户设置为直接替换模式时，系统应尝试将结果写回原输入位置
6. 当快捷键冲突时，系统应提供友好的错误提示和解决建议
7. 当网页端无法实现全局快捷键时，系统应提供页面内快捷键替代方案

### 需求 7：用户界面和体验

**用户故事：** 作为用户，我希望有一个直观易用的界面，以便快速完成各种操作。

#### 验收标准

1. 当用户首次访问时，系统应显示简洁的引导界面
2. 当用户在不同页面间切换时，系统应保持响应速度和状态一致性
3. 当系统处理耗时操作时，系统应显示适当的加载状态
4. 当发生错误时，系统应显示清晰的错误信息和解决建议
5. 当用户在移动设备访问时，系统应提供响应式的界面布局
6. 当用户进行批量操作时，系统应提供进度反馈
7. 当用户需要帮助时，系统应提供内置的使用说明或提示

### 需求 8：数据存储和同步

**用户故事：** 作为用户，我希望我的数据能够安全存储并在需要时进行备份，以便保护我的工作成果。

#### 验收标准

1. 当用户进行任何数据操作时，系统应将数据保存到本地IndexedDB
2. 当用户关闭浏览器重新打开时，系统应能恢复所有用户数据
3. 当用户点击导出数据时，系统应生成包含所有用户数据的备份文件
4. 当用户导入备份文件时，系统应验证数据完整性并合并到现有数据
5. 当数据操作失败时，系统应提供错误恢复机制
6. 当用户清除浏览器数据时，系统应提醒用户备份重要数据
7. 当系统检测到数据损坏时，系统应尝试自动修复或提示用户恢复备份
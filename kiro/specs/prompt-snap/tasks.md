# Prompt-Snap 实现计划

## 任务列表（按优先级排序）

- [x] 1. 项目初始化和基础架构搭建

  - 创建 Tauri 项目结构，配置 React 19 + TypeScript + Vite 开发环境
  - 安装和配置必要的依赖包：shadcn/ui、Tailwind CSS、Zod、React Hook Form 等
  - 使用 @biomejs/biome 替代 ESLint 和 Prettier，配置代码格式化和检查规则
  - 配置 TypeScript 和 Biome 集成
  - 配置 Tauri 插件：SQL、全局快捷键、剪贴板管理
  - _需求: 1.1, 8.1_

- [x] 2. 数据模型和验证层实现

  - [x] 2.1 创建 Zod Schema 定义

    - 实现 Profile、Scene、Prompt、Optimization、AppSettings 的 Schema
    - 添加完整的数据验证规则和错误消息
    - 导出 TypeScript 类型定义
    - _需求: 8.1, 8.2_

  - [x] 2.2 设计 SQLite 数据库结构

    - 编写 database/schema.sql 文件，定义所有表结构
    - 创建必要的索引和外键约束
    - 实现全文搜索索引（FTS5）用于提示词搜索
    - 添加触发器保持 FTS 索引同步
    - _需求: 1.1, 5.6, 8.1_

  - [x] 2.3 实现数据访问层（DAL）
    - 创建 DataAccessLayer 类，封装所有数据库操作
    - 实现 CRUD 操作方法，集成 Zod 验证
    - 添加数据库连接管理和错误处理
    - 实现数据映射方法（数据库行到 TypeScript 对象）
    - _需求: 8.1, 8.2_

- [x] 3. 数据库初始化和迁移系统

  - [x] 3.1 实现数据库初始化逻辑

    - 创建 DatabaseMigration 类管理数据库版本
    - 实现数据库连接和初始化方法
    - 添加默认数据插入（默认画像和场景模板）
    - _需求: 8.1, 8.5_

  - [x] 3.2 创建数据库迁移机制
    - 实现版本管理和迁移执行逻辑
    - 添加迁移脚本管理和回滚功能
    - 确保数据库升级的安全性和一致性
    - _需求: 8.5, 8.6_

- [x] 4. AI 服务集成和优化引擎（基础版本）

  - [x] 4.1 集成 Vercel AI SDK 和 Google Provider

    - 配置 Google Gemini API 连接
    - 实现 AIService 类封装 AI 调用逻辑
    - 添加 API 密钥管理和错误处理
    - 实现基础提示词构建策略（系统提示词 + 用户提示词）
    - _需求: 2.1, 2.2, 2.5_

  - [x] 4.2 实现基础优化引擎逻辑
    - 创建 OptimizationEngine 类协调优化流程
    - 实现基于个人画像的提示词优化（不含模板功能）
    - 添加优化质量评估算法
    - 实现优化历史自动保存功能
    - _需求: 2.1, 2.2, 2.8, 5.1, 5.8_

- [ ] 5. 核心 UI 组件开发（优化功能优先）

  - [ ] 5.1 创建基础布局组件

    - 实现主应用布局（侧边栏 + 主内容区）
    - 创建导航组件和路由配置
    - 添加响应式设计支持
    - 实现主题切换功能（亮色/暗色/系统）
    - _需求: 7.1, 7.2, 7.5_

  - [ ] 5.2 实现基础提示词优化表单组件

    - 创建 OptimizeForm 组件，集成 React Hook Form 和 Zod 验证
    - 实现文本输入区域，支持字符计数和实时验证
    - 添加个人画像选择器（暂不包含场景模板）
    - 实现基础的优化功能
    - _需求: 2.1, 2.2, 2.3, 7.3_

  - [ ] 5.3 创建优化结果显示组件
    - 实现 ResultDisplay 组件展示优化结果
    - 添加原文与优化结果的对比视图
    - 实现复制到剪贴板功能
    - 添加基础的保存功能
    - _需求: 2.1, 2.8, 6.4_

- [ ] 6. 个人画像管理功能

  - [ ] 6.1 实现画像列表和管理界面

    - 创建 ProfileManager 组件显示所有画像
    - 实现画像的增删改查操作
    - 添加画像激活状态切换功能
    - 实现画像搜索和筛选功能
    - _需求: 3.1, 3.2, 3.6_

  - [ ] 6.2 创建画像编辑表单
    - 实现 ProfileEditForm 组件，支持创建和编辑画像
    - 添加表单验证和错误提示
    - 实现技能标签的动态添加和删除
    - 添加画像预览功能
    - _需求: 3.1, 3.2, 3.3, 7.3_

- [ ] 7. Jinja 模板引擎集成（后置优先级）

  - [ ] 7.1 集成 Jinja 模板引擎

    - 安装和配置 JavaScript Jinja 模板引擎（如 nunjucks 或 jinja-js）
    - 创建 TemplateEngine 类封装模板渲染逻辑
    - 实现模板语法验证和错误处理
    - 添加自定义过滤器和函数支持
    - _需求: 4.2, 4.3, 4.6_

  - [ ] 7.2 实现模板变量解析和管理

    - 创建变量解析器，从 Jinja 模板中提取变量定义
    - 实现变量类型推断和验证
    - 添加变量默认值和必填项管理
    - 实现变量依赖关系分析
    - _需求: 4.2, 4.3, 4.6_

  - [ ] 7.3 升级优化引擎支持模板
    - 在现有优化引擎中集成 Jinja 模板功能
    - 实现模板渲染和变量替换
    - 添加模板渲染错误处理和变量验证
    - 更新优化表单组件支持模板变量输入
    - _需求: 2.1, 2.2, 4.6_

- [ ] 8. 场景模板管理功能

  - [ ] 8.1 实现模板列表和分类管理

    - 创建 TemplateList 组件展示所有场景模板
    - 实现按分类筛选和搜索功能
    - 添加内置模板和自定义模板的区分显示
    - 实现模板的启用/禁用状态管理
    - _需求: 4.1, 4.2, 4.4_

  - [ ] 8.2 创建模板编辑器

    - 实现 TemplateEditor 组件支持 Jinja 模板创建和编辑
    - 添加 Jinja 语法高亮和自动补全功能
    - 实现 Jinja 变量的可视化编辑和管理
    - 添加模板语法验证和错误提示
    - 实现模板预览功能，支持 Jinja 变量替换预览
    - 添加常用 Jinja 语法的快速插入功能
    - 添加模板导入导出功能
    - _需求: 4.1, 4.2, 4.3, 4.6_

  - [ ] 8.3 实现从优化结果保存为模板功能
    - 在优化结果页面添加"保存为模板"按钮
    - 实现模板创建对话框，自动填充优化结果
    - 支持用户编辑模板标题、描述和分类
    - 智能识别文本中的可变部分，建议转换为 Jinja 变量
    - 提供 Jinja 变量创建向导，帮助用户定义变量类型和默认值
    - _需求: 4.5, 5.5_

- [ ] 9. 优化历史记录功能

  - [ ] 9.1 实现历史记录列表

    - 创建 HistoryList 组件展示优化历史
    - 实现按时间、画像、模板的筛选功能
    - 添加搜索功能（搜索原文和优化结果）
    - 实现分页加载和虚拟滚动优化
    - _需求: 5.1, 5.2, 5.6, 7.7_

  - [ ] 9.2 创建历史记录详情和对比视图

    - 实现 DiffViewer 组件显示原文与优化结果对比
    - 添加文本差异高亮显示
    - 实现历史记录的回滚功能（复制原文到剪贴板）
    - 添加重新优化功能（使用相同参数重新优化）
    - _需求: 5.2, 5.3, 5.4_

  - [ ] 9.3 实现历史记录管理功能
    - 添加历史记录的删除和批量删除功能
    - 实现历史记录导出功能
    - 添加历史记录统计信息（优化次数、使用的模型等）
    - 实现历史记录的保存为模板功能
    - _需求: 5.5, 5.7, 7.6_

- [ ] 10. Tauri 系统集成功能

  - [ ] 10.1 实现全局快捷键功能

    - 配置 Tauri 全局快捷键插件
    - 实现双击 Ctrl 触发优化的逻辑
    - 添加快捷键注册和注销管理
    - 实现快捷键冲突处理和用户提示
    - _需求: 6.1, 6.2, 6.6_

  - [ ] 10.2 集成剪贴板操作

    - 实现剪贴板内容自动读取功能
    - 添加优化结果自动写入剪贴板
    - 实现剪贴板内容格式检测和处理
    - 添加剪贴板操作的权限管理
    - _需求: 6.3, 6.4, 6.5_

  - [ ] 10.3 实现系统托盘功能
    - 配置系统托盘图标和菜单
    - 实现托盘菜单的快速操作（显示窗口、快速优化、退出）
    - 添加托盘通知功能
    - 实现窗口最小化到托盘的逻辑
    - _需求: 6.7, 7.1_

- [ ] 11. 窗口管理和用户体验

  - [ ] 11.1 实现窗口状态管理

    - 创建 WindowManager 类管理窗口状态
    - 实现窗口位置和大小的持久化
    - 添加窗口居中和自适应功能
    - 实现窗口的显示/隐藏动画效果
    - _需求: 7.1, 7.2_

  - [ ] 11.2 创建快速优化弹窗

    - 实现快速优化对话框，响应全局快捷键
    - 添加简化的优化界面（仅显示必要选项）
    - 实现优化结果的快速预览和操作
    - 添加弹窗的自动关闭和焦点管理
    - _需求: 6.1, 6.4, 7.1_

  - [ ] 11.3 优化用户界面体验
    - 实现加载状态和进度指示器
    - 添加操作反馈和成功/错误提示
    - 实现界面动画和过渡效果
    - 添加键盘快捷键支持（页面内快捷键）
    - _需求: 7.3, 7.4, 7.6_

- [ ] 12. 系统设置和配置管理

  - [ ] 12.1 实现应用设置界面

    - 创建 SettingsPage 组件管理所有应用设置
    - 实现主题设置（亮色/暗色/跟随系统）
    - 添加语言设置和国际化支持
    - 实现自动保存和备份设置
    - _需求: 8.3, 8.4, 8.6_

  - [ ] 12.2 创建 AI 模型配置

    - 实现 ModelConfig 组件管理 AI 相关设置
    - 添加 API 密钥管理（安全存储和验证）
    - 实现模型参数配置（temperature、maxTokens 等）
    - 添加模型连接测试功能
    - _需求: 2.5, 8.3_

  - [ ] 12.3 实现快捷键配置
    - 创建 ShortcutConfig 组件管理快捷键设置
    - 实现快捷键的自定义和冲突检测
    - 添加快捷键重置为默认值功能
    - 实现快捷键的实时预览和测试
    - _需求: 6.1, 6.2, 6.6_

- [ ] 13. 提示词仓库管理（后置优先级）

  - [ ] 13.1 实现提示词列表和搜索功能

    - 创建 PromptList 组件展示所有提示词
    - 实现基于 SQLite FTS5 的全文搜索功能
    - 添加标签筛选和分类筛选
    - 实现收藏功能和收藏列表
    - _需求: 1.1, 1.3, 1.4, 5.6_

  - [ ] 13.2 创建提示词编辑器

    - 实现 PromptEditor 组件支持提示词的创建和编辑
    - 添加标签管理功能（添加、删除、自动补全）
    - 实现提示词预览和格式化功能
    - 支持关联场景模板和个人画像
    - _需求: 1.1, 1.2, 1.5, 1.6_

  - [ ] 13.3 实现批量操作功能
    - 添加提示词的批量选择功能
    - 实现批量删除、批量标签编辑
    - 添加批量导出功能（JSON 格式）
    - 实现批量导入功能，支持数据验证
    - _需求: 1.7, 1.8, 7.6_

- [ ] 14. 错误处理和数据备份

  - [ ] 14.1 实现全局错误处理

    - 创建 ErrorBoundary 组件捕获 React 错误
    - 实现全局错误处理 Hook
    - 添加错误日志记录和上报功能
    - 实现用户友好的错误提示和恢复建议
    - _需求: 7.4, 8.5_

  - [ ] 14.2 实现数据备份和恢复
    - 添加数据库备份功能（导出为 JSON）
    - 实现数据恢复功能（从备份文件导入）
    - 添加自动备份定时任务
    - 实现数据完整性检查和修复
    - _需求: 8.4, 8.5, 8.6, 8.7_

- [ ] 15. 性能优化

  - [ ] 15.1 实现性能优化
    - 优化数据库查询性能，添加查询缓存
    - 实现组件懒加载和代码分割
    - 优化大列表渲染（虚拟滚动）
    - 添加图片和资源的懒加载
    - _需求: 7.5, 7.7_

- [ ] 16. 应用打包和发布准备

  - [ ] 16.1 配置应用打包

    - 配置 Tauri 打包设置和应用图标
    - 实现 macOS 应用签名和公证
    - 添加自动更新机制配置
    - 创建安装包和分发配置
    - _需求: 发布需求_

  - [ ] 16.2 创建用户文档和帮助

    - 编写用户使用指南和快速入门教程
    - 创建应用内帮助系统
    - 添加功能介绍和使用技巧
    - 实现问题反馈和支持渠道
    - _需求: 7.7_

  - [ ] 16.3 最终发布准备
    - 优化应用启动速度和内存使用
    - 完善错误处理和用户体验
    - 准备应用商店发布材料和描述
    - 创建发布说明和更新日志
    - _需求: 所有功能需求_

## 开发优先级说明

**第一阶段（Week 1-2）- 最核心功能**：

- 任务 1-3：基础架构搭建
- 任务 5：AI 服务集成（不含模板功能）
- 任务 6：基础 UI 和提示词优化表单
- 任务 7：个人画像管理
- 重点：实现最基本的提示词优化功能，支持个人画像

**第二阶段（Week 3-4）- 扩展功能**：

- 任务 4：Jinja 模板引擎集成
- 任务 8：场景模板管理
- 任务 9：优化历史记录
- 重点：添加模板系统和历史记录功能

**第三阶段（Week 5-6）- 系统集成**：

- 任务 10-12：Tauri 系统功能、窗口管理、设置
- 重点：实现桌面应用特有的系统级功能

**第四阶段（Week 7）- 完善和发布**：

- 任务 13-16：提示词仓库、错误处理、性能优化、发布
- 重点：完善所有功能并准备发布

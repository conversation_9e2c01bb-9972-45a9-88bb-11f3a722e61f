# Prompt-Snap 设计文档

## 概述

Prompt-Snap 是一个智能提示词优化桌面应用，采用现代化的技术栈构建。系统通过 Vercel AI SDK 集成 Google Gemini 模型，为用户提供个性化的提示词优化服务。

**开发策略**: 采用渐进式开发方法，初期开发网页端版本便于快速迭代和功能验证，后续通过 Tauri 框架封装为跨平台桌面应用。这种方法允许我们：
- 快速验证核心功能和用户体验
- 使用熟悉的 Web 技术栈加速开发
- 在网页端完善功能后无缝迁移到桌面应用
- 保持代码复用性和维护性

## 架构设计

### 整体架构

```mermaid
graph TB
    A[Tauri 桌面应用] --> B[前端界面层]
    A --> C[系统集成层]
    B --> D[业务逻辑层]
    D --> E[数据存储层]
    D --> F[AI服务层]
    
    B --> B1[React 19 组件]
    B --> B2[shadcn/ui 组件库]
    B --> B3[响应式布局]
    
    C --> C1[全局快捷键]
    C --> C2[剪贴板操作]
    C --> C3[系统托盘]
    C --> C4[窗口管理]
    
    D --> D1[提示词管理]
    D --> D2[优化引擎]
    D --> D3[历史记录]
    D --> D4[模板系统]
    
    E --> E1[IndexedDB]
    E --> E2[Dexie.js]
    E --> E3[Tauri 存储]
    
    F --> F1[Vercel AI SDK]
    F --> F2[Google Provider]
    F --> F3[Gemini 模型]
```

### 技术栈

**阶段一：网页端开发**
- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite
- **包管理器**: bun
- **UI组件库**: shadcn/ui
- **路由**: React Router v6
- **状态管理**: React Context + useReducer
- **数据验证**: Zod (Schema 验证和类型安全)
- **数据存储**: IndexedDB (通过 Dexie.js)
- **AI集成**: Vercel AI SDK + Google Provider
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **模板引擎**: nunjucks (Jinja-like 模板支持)

**阶段二：桌面应用迁移**
- **桌面应用框架**: Tauri v2
- **数据存储**: SQLite (通过 Tauri SQL 插件)
- **系统集成**: Tauri APIs (剪贴板、全局快捷键、系统托盘)
- **文件系统**: Tauri filesystem APIs

## 组件和接口设计

### 核心组件架构

```mermaid
graph TD
    A[App] --> B[Layout]
    B --> C[Sidebar]
    B --> D[MainContent]
    
    D --> E[OptimizePage]
    D --> F[LibraryPage]
    D --> G[HistoryPage]
    D --> H[TemplatesPage]
    D --> I[SettingsPage]
    
    E --> E1[OptimizeForm]
    E --> E2[ResultDisplay]
    E --> E3[ProfileSelector]
    E --> E4[TemplateSelector]
    
    F --> F1[PromptList]
    F --> F2[SearchBar]
    F --> F3[TagFilter]
    F --> F4[PromptEditor]
    
    G --> G1[HistoryList]
    G --> G2[DiffViewer]
    G --> G3[HistoryActions]
    
    H --> H1[TemplateList]
    H --> H2[TemplateEditor]
    H --> H3[VariablePreview]
    
    I --> I1[ProfileManager]
    I --> I2[ModelConfig]
    I --> I3[ShortcutConfig]
```

### 数据模型设计

基于 SQLite 和 Zod 验证的数据模型：

```typescript
// schemas/index.ts
import { z } from 'zod';

// 基础 Schema
const BaseEntitySchema = z.object({
  id: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// 变量 Schema
export const VariableSchema = z.object({
  name: z.string().min(1, '变量名不能为空').max(50, '变量名过长'),
  description: z.string().max(200, '描述过长'),
  defaultValue: z.string().optional(),
  required: z.boolean().default(false),
});

// 个人画像 Schema
export const ProfileSchema = BaseEntitySchema.extend({
  name: z.string().min(1, '画像名称不能为空').max(50, '画像名称过长'),
  role: z.string().min(1, '角色不能为空').max(100, '角色描述过长'),
  skills: z.array(z.string().min(1).max(50)).max(20, '技能数量过多'),
  tone: z.string().min(1, '语调不能为空').max(100, '语调描述过长'),
  isActive: z.boolean().default(false),
});

// 场景模板 Schema
export const SceneSchema = BaseEntitySchema.extend({
  title: z.string().min(1, '模板标题不能为空').max(100, '标题过长'),
  description: z.string().max(500, '描述过长'),
  template: z.string().min(1, '模板内容不能为空').max(5000, '模板内容过长'),
  variables: z.array(VariableSchema).max(10, '变量数量过多'),
  category: z.string().min(1, '分类不能为空').max(50, '分类名称过长'),
  isBuiltIn: z.boolean().default(false),
});

// 提示词 Schema
export const PromptSchema = BaseEntitySchema.extend({
  title: z.string().min(1, '提示词标题不能为空').max(200, '标题过长'),
  content: z.string().min(1, '提示词内容不能为空').max(10000, '内容过长'),
  tags: z.array(z.string().min(1).max(30)).max(10, '标签数量过多'),
  sceneId: z.string().uuid().optional(),
  profileId: z.string().uuid().optional(),
  isFavorite: z.boolean().default(false),
});

// 优化元数据 Schema
export const OptimizationMetadataSchema = z.object({
  tokensUsed: z.number().min(0),
  processingTime: z.number().min(0),
  quality: z.number().min(0).max(100),
});

// 优化历史 Schema
export const OptimizationSchema = BaseEntitySchema.extend({
  original: z.string().min(1, '原始内容不能为空').max(10000, '原始内容过长'),
  optimized: z.string().min(1, '优化内容不能为空').max(10000, '优化内容过长'),
  sceneId: z.string().uuid().optional(),
  profileId: z.string().uuid().optional(),
  modelUsed: z.string().min(1, '模型名称不能为空'),
  timestamp: z.date(),
  metadata: OptimizationMetadataSchema,
});

// 快捷键配置 Schema
export const ShortcutsSchema = z.object({
  optimize: z.string().min(1, '优化快捷键不能为空'),
  newPrompt: z.string().min(1, '新建提示词快捷键不能为空'),
  search: z.string().min(1, '搜索快捷键不能为空'),
});

// AI 配置 Schema
export const AIConfigSchema = z.object({
  model: z.string().min(1, '模型名称不能为空'),
  temperature: z.number().min(0).max(2),
  maxTokens: z.number().min(1).max(8192),
  apiKey: z.string().optional(),
});

// 应用设置 Schema
export const AppSettingsSchema = BaseEntitySchema.extend({
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().default('zh-CN'),
  shortcuts: ShortcutsSchema,
  aiConfig: AIConfigSchema,
  autoSave: z.boolean().default(true),
  backupEnabled: z.boolean().default(true),
});

// 导出类型
export type Profile = z.infer<typeof ProfileSchema>;
export type Scene = z.infer<typeof SceneSchema>;
export type Variable = z.infer<typeof VariableSchema>;
export type Prompt = z.infer<typeof PromptSchema>;
export type Optimization = z.infer<typeof OptimizationSchema>;
export type OptimizationMetadata = z.infer<typeof OptimizationMetadataSchema>;
export type AppSettings = z.infer<typeof AppSettingsSchema>;
export type Shortcuts = z.infer<typeof ShortcutsSchema>;
export type AIConfig = z.infer<typeof AIConfigSchema>;
```

### 模板引擎设计

为了支持灵活的场景模板功能，系统集成了基于 Jinja 语法的模板引擎，使用 nunjucks 库实现。

#### 模板语法支持

```typescript
// services/template-engine.ts
import nunjucks from 'nunjucks';
import { Variable, Scene } from '../schemas';

export class TemplateEngine {
  private env: nunjucks.Environment;

  constructor() {
    this.env = new nunjucks.Environment();
    this.setupFilters();
  }

  /**
   * 渲染模板
   * @param template 模板字符串
   * @param variables 变量值映射
   * @returns 渲染后的字符串
   */
  render(template: string, variables: Record<string, string> = {}): string {
    try {
      return this.env.renderString(template, variables);
    } catch (error) {
      throw new TemplateRenderError(`模板渲染失败: ${error.message}`);
    }
  }

  /**
   * 验证模板语法
   * @param template 模板字符串
   * @returns 验证结果
   */
  validateTemplate(template: string): TemplateValidationResult {
    try {
      // 尝试解析模板
      this.env.renderString(template, {});
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error.message,
        line: this.extractLineNumber(error.message)
      };
    }
  }

  /**
   * 提取模板中的变量
   * @param template 模板字符串
   * @returns 变量名数组
   */
  extractVariables(template: string): string[] {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g;
    const variables = new Set<string>();
    let match;

    while ((match = variableRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }

    return Array.from(variables);
  }

  /**
   * 生成模板预览
   * @param scene 场景模板
   * @param variables 变量值
   * @returns 预览结果
   */
  generatePreview(scene: Scene, variables: Record<string, string> = {}): TemplatePreview {
    // 为未提供的变量生成默认值
    const defaultVariables = { ...variables };
    scene.variables.forEach(variable => {
      if (!defaultVariables[variable.name]) {
        defaultVariables[variable.name] = variable.defaultValue || `[${variable.name}]`;
      }
    });

    try {
      const rendered = this.render(scene.template, defaultVariables);
      return {
        success: true,
        rendered,
        variables: defaultVariables
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        variables: defaultVariables
      };
    }
  }

  private setupFilters(): void {
    // 添加自定义过滤器
    this.env.addFilter('upper', (str: string) => str.toUpperCase());
    this.env.addFilter('lower', (str: string) => str.toLowerCase());
    this.env.addFilter('capitalize', (str: string) => 
      str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
    );
    this.env.addFilter('truncate', (str: string, length: number = 100) => 
      str.length > length ? str.substring(0, length) + '...' : str
    );
  }

  private extractLineNumber(errorMessage: string): number | undefined {
    const lineMatch = errorMessage.match(/line (\d+)/);
    return lineMatch ? parseInt(lineMatch[1], 10) : undefined;
  }
}

// 类型定义
interface TemplateValidationResult {
  isValid: boolean;
  error?: string;
  line?: number;
}

interface TemplatePreview {
  success: boolean;
  rendered?: string;
  error?: string;
  variables: Record<string, string>;
}

export class TemplateRenderError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TemplateRenderError';
  }
}

export const templateEngine = new TemplateEngine();
```

#### 模板语法示例

```typescript
// 支持的模板语法示例
const templateExamples = {
  // 基础变量替换
  basic: "请优化以下{{type}}相关的提示词：{{content}}",
  
  // 条件语句
  conditional: `
    {% if role %}
    作为{{role}}，请优化以下提示词：
    {% else %}
    请优化以下提示词：
    {% endif %}
    {{content}}
  `,
  
  // 循环语句
  loop: `
    请基于以下技能优化提示词：
    {% for skill in skills %}
    - {{skill}}
    {% endfor %}
    
    原始提示词：{{content}}
  `,
  
  // 过滤器使用
  filters: `
    请优化以下{{type | upper}}提示词：
    {{content | truncate(200)}}
    
    要求：{{requirements | capitalize}}
  `
};
```

### 数据库架构

#### SQLite 数据库结构

```sql
-- database/schema.sql

-- 个人画像表
CREATE TABLE IF NOT EXISTS profiles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    skills TEXT NOT NULL, -- JSON 数组
    tone TEXT NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 场景模板表
CREATE TABLE IF NOT EXISTS scenes (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    template TEXT NOT NULL,
    variables TEXT, -- JSON 数组
    category TEXT NOT NULL,
    is_built_in BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 提示词表
CREATE TABLE IF NOT EXISTS prompts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT, -- JSON 数组
    scene_id TEXT,
    profile_id TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);

-- 优化历史表
CREATE TABLE IF NOT EXISTS optimizations (
    id TEXT PRIMARY KEY,
    original TEXT NOT NULL,
    optimized TEXT NOT NULL,
    scene_id TEXT,
    profile_id TEXT,
    model_used TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT NOT NULL, -- JSON 对象
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);

-- 应用设置表
CREATE TABLE IF NOT EXISTS settings (
    id TEXT PRIMARY KEY,
    theme TEXT DEFAULT 'system',
    language TEXT DEFAULT 'zh-CN',
    shortcuts TEXT NOT NULL, -- JSON 对象
    ai_config TEXT NOT NULL, -- JSON 对象
    auto_save BOOLEAN DEFAULT TRUE,
    backup_enabled BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_created ON profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_scenes_category ON scenes(category);
CREATE INDEX IF NOT EXISTS idx_scenes_built_in ON scenes(is_built_in);
CREATE INDEX IF NOT EXISTS idx_prompts_favorite ON prompts(is_favorite);
CREATE INDEX IF NOT EXISTS idx_prompts_created ON prompts(created_at);
CREATE INDEX IF NOT EXISTS idx_prompts_scene ON prompts(scene_id);
CREATE INDEX IF NOT EXISTS idx_prompts_profile ON prompts(profile_id);
CREATE INDEX IF NOT EXISTS idx_optimizations_timestamp ON optimizations(timestamp);
CREATE INDEX IF NOT EXISTS idx_optimizations_profile ON optimizations(profile_id);

-- 全文搜索索引 (用于提示词搜索)
CREATE VIRTUAL TABLE IF NOT EXISTS prompts_fts USING fts5(
    title, content, tags,
    content='prompts',
    content_rowid='rowid'
);

-- 触发器：保持 FTS 索引同步
CREATE TRIGGER IF NOT EXISTS prompts_fts_insert AFTER INSERT ON prompts BEGIN
    INSERT INTO prompts_fts(rowid, title, content, tags) 
    VALUES (new.rowid, new.title, new.content, new.tags);
END;

CREATE TRIGGER IF NOT EXISTS prompts_fts_delete AFTER DELETE ON prompts BEGIN
    DELETE FROM prompts_fts WHERE rowid = old.rowid;
END;

CREATE TRIGGER IF NOT EXISTS prompts_fts_update AFTER UPDATE ON prompts BEGIN
    DELETE FROM prompts_fts WHERE rowid = old.rowid;
    INSERT INTO prompts_fts(rowid, title, content, tags) 
    VALUES (new.rowid, new.title, new.content, new.tags);
END;
```

#### 数据访问层 (DAL) 与验证

```typescript
// database/dal.ts
import Database from '@tauri-apps/plugin-sql';
import { 
  Profile, Scene, Prompt, Optimization, AppSettings,
  ProfileSchema, SceneSchema, PromptSchema, OptimizationSchema, AppSettingsSchema
} from '../schemas';

export class DataAccessLayer {
  private db: Database | null = null;

  async initialize(): Promise<void> {
    try {
      // 连接到 SQLite 数据库
      this.db = await Database.load('sqlite:prompt_snap.db');
      
      // 执行数据库初始化脚本
      await this.executeSchema();
      
      console.log('数据库初始化成功');
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  private async executeSchema(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化');

    // 读取并执行 schema.sql
    const schemaSQL = await fetch('/database/schema.sql').then(r => r.text());
    const statements = schemaSQL.split(';').filter(s => s.trim());
    
    for (const statement of statements) {
      if (statement.trim()) {
        await this.db.execute(statement);
      }
    }
  }

  // 个人画像操作
  async createProfile(data: Omit<Profile, 'id' | 'createdAt' | 'updatedAt'>): Promise<Profile> {
    if (!this.db) throw new Error('数据库未初始化');

    const profile: Profile = {
      ...data,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = ProfileSchema.parse(profile);
    
    await this.db.execute(
      `INSERT INTO profiles (id, name, role, skills, tone, is_active, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        validated.id,
        validated.name,
        validated.role,
        JSON.stringify(validated.skills),
        validated.tone,
        validated.isActive,
        validated.createdAt.toISOString(),
        validated.updatedAt.toISOString(),
      ]
    );
    
    return validated;
  }

  async getProfile(id: string): Promise<Profile | null> {
    if (!this.db) throw new Error('数据库未初始化');

    const result = await this.db.select<any[]>(
      'SELECT * FROM profiles WHERE id = ?',
      [id]
    );

    if (result.length === 0) return null;

    const row = result[0];
    return this.mapRowToProfile(row);
  }

  async getAllProfiles(): Promise<Profile[]> {
    if (!this.db) throw new Error('数据库未初始化');

    const result = await this.db.select<any[]>(
      'SELECT * FROM profiles ORDER BY created_at DESC'
    );

    return result.map(row => this.mapRowToProfile(row));
  }

  async updateProfile(id: string, data: Partial<Omit<Profile, 'id' | 'createdAt'>>): Promise<Profile> {
    if (!this.db) throw new Error('数据库未初始化');

    const existing = await this.getProfile(id);
    if (!existing) {
      throw new Error('画像不存在');
    }

    const updated = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    const validated = ProfileSchema.parse(updated);

    await this.db.execute(
      `UPDATE profiles 
       SET name = ?, role = ?, skills = ?, tone = ?, is_active = ?, updated_at = ?
       WHERE id = ?`,
      [
        validated.name,
        validated.role,
        JSON.stringify(validated.skills),
        validated.tone,
        validated.isActive,
        validated.updatedAt.toISOString(),
        id,
      ]
    );

    return validated;
  }

  async deleteProfile(id: string): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化');

    await this.db.execute('DELETE FROM profiles WHERE id = ?', [id]);
  }

  // 场景模板操作
  async createScene(data: Omit<Scene, 'id' | 'createdAt' | 'updatedAt'>): Promise<Scene> {
    if (!this.db) throw new Error('数据库未初始化');

    const scene: Scene = {
      ...data,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = SceneSchema.parse(scene);
    
    await this.db.execute(
      `INSERT INTO scenes (id, title, description, template, variables, category, is_built_in, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        validated.id,
        validated.title,
        validated.description,
        validated.template,
        JSON.stringify(validated.variables),
        validated.category,
        validated.isBuiltIn,
        validated.createdAt.toISOString(),
        validated.updatedAt.toISOString(),
      ]
    );
    
    return validated;
  }

  // 提示词操作
  async createPrompt(data: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'>): Promise<Prompt> {
    if (!this.db) throw new Error('数据库未初始化');

    const prompt: Prompt = {
      ...data,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = PromptSchema.parse(prompt);
    
    await this.db.execute(
      `INSERT INTO prompts (id, title, content, tags, scene_id, profile_id, is_favorite, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        validated.id,
        validated.title,
        validated.content,
        JSON.stringify(validated.tags),
        validated.sceneId || null,
        validated.profileId || null,
        validated.isFavorite,
        validated.createdAt.toISOString(),
        validated.updatedAt.toISOString(),
      ]
    );
    
    return validated;
  }

  async searchPrompts(query: string, limit = 50): Promise<Prompt[]> {
    if (!this.db) throw new Error('数据库未初始化');

    const result = await this.db.select<any[]>(
      `SELECT p.* FROM prompts p
       JOIN prompts_fts fts ON p.rowid = fts.rowid
       WHERE prompts_fts MATCH ?
       ORDER BY rank
       LIMIT ?`,
      [query, limit]
    );

    return result.map(row => this.mapRowToPrompt(row));
  }

  // 优化历史操作
  async createOptimization(data: Omit<Optimization, 'id' | 'createdAt' | 'updatedAt'>): Promise<Optimization> {
    if (!this.db) throw new Error('数据库未初始化');

    const optimization: Optimization = {
      ...data,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const validated = OptimizationSchema.parse(optimization);
    
    await this.db.execute(
      `INSERT INTO optimizations (id, original, optimized, scene_id, profile_id, model_used, timestamp, metadata, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        validated.id,
        validated.original,
        validated.optimized,
        validated.sceneId || null,
        validated.profileId || null,
        validated.modelUsed,
        validated.timestamp.toISOString(),
        JSON.stringify(validated.metadata),
        validated.createdAt.toISOString(),
        validated.updatedAt.toISOString(),
      ]
    );
    
    return validated;
  }

  async getOptimizationHistory(limit = 50): Promise<Optimization[]> {
    if (!this.db) throw new Error('数据库未初始化');

    const result = await this.db.select<any[]>(
      'SELECT * FROM optimizations ORDER BY timestamp DESC LIMIT ?',
      [limit]
    );

    return result.map(row => this.mapRowToOptimization(row));
  }

  // 设置操作
  async getSettings(): Promise<AppSettings | null> {
    if (!this.db) throw new Error('数据库未初始化');

    const result = await this.db.select<any[]>('SELECT * FROM settings LIMIT 1');
    
    if (result.length === 0) return null;

    return this.mapRowToSettings(result[0]);
  }

  async updateSettings(data: Partial<Omit<AppSettings, 'id' | 'createdAt'>>): Promise<AppSettings> {
    if (!this.db) throw new Error('数据库未初始化');

    let existing = await this.getSettings();
    
    if (!existing) {
      // 创建默认设置
      existing = {
        id: crypto.randomUUID(),
        theme: 'system',
        language: 'zh-CN',
        shortcuts: {
          optimize: 'Ctrl+Ctrl',
          newPrompt: 'Ctrl+N',
          search: 'Ctrl+F',
        },
        aiConfig: {
          model: 'gemini-1.5-pro',
          temperature: 0.7,
          maxTokens: 2000,
        },
        autoSave: true,
        backupEnabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    const updated = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    const validated = AppSettingsSchema.parse(updated);

    if (existing.id === validated.id) {
      // 更新现有设置
      await this.db.execute(
        `UPDATE settings 
         SET theme = ?, language = ?, shortcuts = ?, ai_config = ?, auto_save = ?, backup_enabled = ?, updated_at = ?
         WHERE id = ?`,
        [
          validated.theme,
          validated.language,
          JSON.stringify(validated.shortcuts),
          JSON.stringify(validated.aiConfig),
          validated.autoSave,
          validated.backupEnabled,
          validated.updatedAt.toISOString(),
          validated.id,
        ]
      );
    } else {
      // 插入新设置
      await this.db.execute(
        `INSERT INTO settings (id, theme, language, shortcuts, ai_config, auto_save, backup_enabled, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          validated.id,
          validated.theme,
          validated.language,
          JSON.stringify(validated.shortcuts),
          JSON.stringify(validated.aiConfig),
          validated.autoSave,
          validated.backupEnabled,
          validated.createdAt.toISOString(),
          validated.updatedAt.toISOString(),
        ]
      );
    }

    return validated;
  }

  // 数据映射辅助方法
  private mapRowToProfile(row: any): Profile {
    return ProfileSchema.parse({
      id: row.id,
      name: row.name,
      role: row.role,
      skills: JSON.parse(row.skills),
      tone: row.tone,
      isActive: Boolean(row.is_active),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    });
  }

  private mapRowToPrompt(row: any): Prompt {
    return PromptSchema.parse({
      id: row.id,
      title: row.title,
      content: row.content,
      tags: JSON.parse(row.tags || '[]'),
      sceneId: row.scene_id || undefined,
      profileId: row.profile_id || undefined,
      isFavorite: Boolean(row.is_favorite),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    });
  }

  private mapRowToOptimization(row: any): Optimization {
    return OptimizationSchema.parse({
      id: row.id,
      original: row.original,
      optimized: row.optimized,
      sceneId: row.scene_id || undefined,
      profileId: row.profile_id || undefined,
      modelUsed: row.model_used,
      timestamp: new Date(row.timestamp),
      metadata: JSON.parse(row.metadata),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    });
  }

  private mapRowToSettings(row: any): AppSettings {
    return AppSettingsSchema.parse({
      id: row.id,
      theme: row.theme,
      language: row.language,
      shortcuts: JSON.parse(row.shortcuts),
      aiConfig: JSON.parse(row.ai_config),
      autoSave: Boolean(row.auto_save),
      backupEnabled: Boolean(row.backup_enabled),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    });
  }

  // 数据导出功能
  async exportData(options: ExportOptions = {}): Promise<ExportData> {
    if (!this.db) throw new Error('数据库未初始化');

    const exportData: ExportData = {
      version: '1.0',
      exportedAt: new Date().toISOString(),
      data: {}
    };

    // 导出个人画像
    if (options.includeProfiles !== false) {
      const profiles = await this.getAllProfiles();
      exportData.data.profiles = profiles;
    }

    // 导出场景模板
    if (options.includeScenes !== false) {
      const scenes = await this.getAllScenes();
      exportData.data.scenes = scenes;
    }

    // 导出提示词
    if (options.includePrompts !== false) {
      const prompts = await this.getAllPrompts();
      exportData.data.prompts = prompts;
    }

    // 导出优化历史（可选）
    if (options.includeHistory === true) {
      const history = await this.getOptimizationHistory(1000);
      exportData.data.optimizations = history;
    }

    // 导出设置
    if (options.includeSettings !== false) {
      const settings = await this.getSettings();
      if (settings) {
        // 移除敏感信息
        const sanitizedSettings = { ...settings };
        if (sanitizedSettings.aiConfig.apiKey) {
          sanitizedSettings.aiConfig.apiKey = '[REDACTED]';
        }
        exportData.data.settings = sanitizedSettings;
      }
    }

    return exportData;
  }

  // 数据导入功能
  async importData(importData: ExportData, options: ImportOptions = {}): Promise<ImportResult> {
    if (!this.db) throw new Error('数据库未初始化');

    const result: ImportResult = {
      success: true,
      imported: {
        profiles: 0,
        scenes: 0,
        prompts: 0,
        optimizations: 0
      },
      errors: []
    };

    try {
      // 验证导入数据格式
      const validatedData = ExportDataSchema.parse(importData);

      // 开始事务
      await this.db.execute('BEGIN TRANSACTION');

      // 导入个人画像
      if (validatedData.data.profiles && options.importProfiles !== false) {
        for (const profile of validatedData.data.profiles) {
          try {
            if (options.overwriteExisting) {
              await this.updateProfile(profile.id, profile);
            } else {
              // 检查是否已存在
              const existing = await this.getProfile(profile.id);
              if (!existing) {
                await this.createProfile(profile);
                result.imported.profiles++;
              }
            }
          } catch (error) {
            result.errors.push(`导入画像 ${profile.name} 失败: ${error.message}`);
          }
        }
      }

      // 导入场景模板
      if (validatedData.data.scenes && options.importScenes !== false) {
        for (const scene of validatedData.data.scenes) {
          try {
            if (options.overwriteExisting) {
              await this.updateScene(scene.id, scene);
            } else {
              const existing = await this.getScene(scene.id);
              if (!existing) {
                await this.createScene(scene);
                result.imported.scenes++;
              }
            }
          } catch (error) {
            result.errors.push(`导入模板 ${scene.title} 失败: ${error.message}`);
          }
        }
      }

      // 导入提示词
      if (validatedData.data.prompts && options.importPrompts !== false) {
        for (const prompt of validatedData.data.prompts) {
          try {
            if (options.overwriteExisting) {
              await this.updatePrompt(prompt.id, prompt);
            } else {
              const existing = await this.getPrompt(prompt.id);
              if (!existing) {
                await this.createPrompt(prompt);
                result.imported.prompts++;
              }
            }
          } catch (error) {
            result.errors.push(`导入提示词 ${prompt.title} 失败: ${error.message}`);
          }
        }
      }

      // 导入优化历史
      if (validatedData.data.optimizations && options.importHistory === true) {
        for (const optimization of validatedData.data.optimizations) {
          try {
            await this.createOptimization(optimization);
            result.imported.optimizations++;
          } catch (error) {
            result.errors.push(`导入历史记录失败: ${error.message}`);
          }
        }
      }

      // 提交事务
      await this.db.execute('COMMIT');

    } catch (error) {
      // 回滚事务
      await this.db.execute('ROLLBACK');
      result.success = false;
      result.errors.push(`导入失败: ${error.message}`);
    }

    return result;
  }

  // 数据备份和恢复
  async createBackup(): Promise<string> {
    const exportData = await this.exportData({
      includeProfiles: true,
      includeScenes: true,
      includePrompts: true,
      includeHistory: true,
      includeSettings: true
    });

    const backupFileName = `prompt-snap-backup-${new Date().toISOString().split('T')[0]}.json`;
    const backupContent = JSON.stringify(exportData, null, 2);

    // 在桌面应用中，使用 Tauri 文件系统 API 保存
    // 在网页端，触发下载
    return backupContent;
  }

  async restoreFromBackup(backupContent: string): Promise<ImportResult> {
    try {
      const backupData = JSON.parse(backupContent);
      return await this.importData(backupData, {
        importProfiles: true,
        importScenes: true,
        importPrompts: true,
        importHistory: true,
        overwriteExisting: true
      });
    } catch (error) {
      throw new Error(`备份恢复失败: ${error.message}`);
    }
  }
}

// 导出/导入相关类型定义
interface ExportOptions {
  includeProfiles?: boolean;
  includeScenes?: boolean;
  includePrompts?: boolean;
  includeHistory?: boolean;
  includeSettings?: boolean;
}

interface ImportOptions {
  importProfiles?: boolean;
  importScenes?: boolean;
  importPrompts?: boolean;
  importHistory?: boolean;
  overwriteExisting?: boolean;
}

interface ExportData {
  version: string;
  exportedAt: string;
  data: {
    profiles?: Profile[];
    scenes?: Scene[];
    prompts?: Prompt[];
    optimizations?: Optimization[];
    settings?: AppSettings;
  };
}

interface ImportResult {
  success: boolean;
  imported: {
    profiles: number;
    scenes: number;
    prompts: number;
    optimizations: number;
  };
  errors: string[];
}

// 导出数据验证 Schema
const ExportDataSchema = z.object({
  version: z.string(),
  exportedAt: z.string(),
  data: z.object({
    profiles: z.array(ProfileSchema).optional(),
    scenes: z.array(SceneSchema).optional(),
    prompts: z.array(PromptSchema).optional(),
    optimizations: z.array(OptimizationSchema).optional(),
    settings: AppSettingsSchema.optional(),
  })
});

export const dal = new DataAccessLayer();

// 数据库迁移管理
export class DatabaseMigration {
  private db: Database;

  constructor(db: Database) {
    this.db = db;
  }

  async getCurrentVersion(): Promise<number> {
    try {
      const result = await this.db.select<{version: number}[]>(
        'SELECT version FROM schema_version ORDER BY version DESC LIMIT 1'
      );
      return result.length > 0 ? result[0].version : 0;
    } catch {
      // 如果表不存在，说明是全新数据库
      return 0;
    }
  }

  async migrate(): Promise<void> {
    const currentVersion = await this.getCurrentVersion();
    const targetVersion = 1; // 当前最新版本

    if (currentVersion >= targetVersion) {
      console.log('数据库已是最新版本');
      return;
    }

    console.log(`开始数据库迁移: v${currentVersion} -> v${targetVersion}`);

    // 创建版本管理表
    if (currentVersion === 0) {
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS schema_version (
          version INTEGER PRIMARY KEY,
          applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    }

    // 执行迁移
    for (let version = currentVersion + 1; version <= targetVersion; version++) {
      await this.applyMigration(version);
      await this.db.execute(
        'INSERT INTO schema_version (version) VALUES (?)',
        [version]
      );
      console.log(`已应用迁移 v${version}`);
    }

    console.log('数据库迁移完成');
  }

  private async applyMigration(version: number): Promise<void> {
    switch (version) {
      case 1:
        // 初始数据库结构
        await this.createInitialSchema();
        break;
      default:
        throw new Error(`未知的迁移版本: ${version}`);
    }
  }

  private async createInitialSchema(): Promise<void> {
    // 执行初始 schema.sql
    const schemaSQL = await fetch('/database/schema.sql').then(r => r.text());
    const statements = schemaSQL.split(';').filter(s => s.trim());
    
    for (const statement of statements) {
      if (statement.trim()) {
        await this.db.execute(statement);
      }
    }

    // 插入默认数据
    await this.insertDefaultData();
  }

  private async insertDefaultData(): Promise<void> {
    // 插入默认个人画像
    const defaultProfiles = [
      {
        id: crypto.randomUUID(),
        name: '通用助手',
        role: '智能助手',
        skills: JSON.stringify(['文本处理', '内容优化', '逻辑分析']),
        tone: '专业、友好、准确',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ];

    for (const profile of defaultProfiles) {
      await this.db.execute(
        `INSERT INTO profiles (id, name, role, skills, tone, is_active, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        Object.values(profile)
      );
    }

    // 插入默认场景模板
    const defaultScenes = [
      {
        id: crypto.randomUUID(),
        title: '代码优化',
        description: '优化编程相关的提示词',
        template: '请优化以下代码相关的提示词，使其更加精确和专业：{{content}}',
        variables: JSON.stringify([
          { name: 'content', description: '要优化的内容', required: true }
        ]),
        category: '编程',
        is_built_in: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: crypto.randomUUID(),
        title: '文案创作',
        description: '优化文案和营销相关的提示词',
        template: '请优化以下文案提示词，使其更具吸引力和说服力：{{content}}',
        variables: JSON.stringify([
          { name: 'content', description: '要优化的内容', required: true }
        ]),
        category: '文案',
        is_built_in: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ];

    for (const scene of defaultScenes) {
      await this.db.execute(
        `INSERT INTO scenes (id, title, description, template, variables, category, is_built_in, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        Object.values(scene)
      );
    }
  }
}
```

## 系统集成设计

### Tauri 系统集成

#### 全局快捷键实现
```typescript
// tauri-commands.ts
import { register, unregister } from '@tauri-apps/plugin-global-shortcut';
import { invoke } from '@tauri-apps/api/core';

export class TauriSystemService {
  async registerGlobalShortcut(shortcut: string) {
    try {
      await register(shortcut, async () => {
        // 获取剪贴板内容
        const clipboardText = await this.getClipboardText();
        if (clipboardText) {
          // 触发优化流程
          await invoke('trigger_optimization', { text: clipboardText });
        }
      });
    } catch (error) {
      console.error('注册全局快捷键失败:', error);
    }
  }

  async getClipboardText(): Promise<string | null> {
    try {
      return await invoke('get_clipboard_text');
    } catch (error) {
      console.error('读取剪贴板失败:', error);
      return null;
    }
  }

  async setClipboardText(text: string): Promise<void> {
    try {
      await invoke('set_clipboard_text', { text });
    } catch (error) {
      console.error('写入剪贴板失败:', error);
    }
  }

  async showMainWindow(): Promise<void> {
    await invoke('show_main_window');
  }

  async hideToTray(): Promise<void> {
    await invoke('hide_to_tray');
  }
}
```

#### 系统托盘配置
```rust
// src-tauri/src/main.rs (基本结构，避免复杂 Rust 开发)
use tauri::{
    CustomMenuItem, SystemTray, SystemTrayEvent, SystemTrayMenu, 
    Manager, AppHandle, Window
};

fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示主窗口");
    let optimize = CustomMenuItem::new("optimize".to_string(), "快速优化");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(optimize)
        .add_item(show)
        .add_separator()
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}

fn handle_system_tray_event(app: &AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                    window.set_focus().unwrap();
                }
                "optimize" => {
                    // 触发快速优化
                    app.emit_all("quick-optimize", {}).unwrap();
                }
                _ => {}
            }
        }
        _ => {}
    }
}
```

#### 窗口管理
```typescript
// window-manager.ts
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { PhysicalPosition, PhysicalSize } from '@tauri-apps/api/window';

export class WindowManager {
  private mainWindow: WebviewWindow | null = null;

  async createMainWindow() {
    this.mainWindow = new WebviewWindow('main', {
      url: '/',
      title: 'Prompt Snap',
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      center: true,
      resizable: true,
      decorations: true,
      alwaysOnTop: false,
    });

    return this.mainWindow;
  }

  async showQuickOptimizeDialog(text: string) {
    const quickWindow = new WebviewWindow('quick-optimize', {
      url: `/quick-optimize?text=${encodeURIComponent(text)}`,
      title: '快速优化',
      width: 600,
      height: 400,
      center: true,
      resizable: false,
      decorations: true,
      alwaysOnTop: true,
      skipTaskbar: true,
    });

    return quickWindow;
  }

  async centerWindow() {
    if (this.mainWindow) {
      await this.mainWindow.center();
    }
  }

  async hideToTray() {
    if (this.mainWindow) {
      await this.mainWindow.hide();
    }
  }
}
```

## AI 服务集成

### Vercel AI SDK 配置

```typescript
// ai-service.ts
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';

export class AIService {
  private provider;

  constructor(apiKey: string) {
    this.provider = google({
      apiKey: apiKey
    });
  }

  async optimizePrompt(
    original: string,
    profile: Profile,
    scene?: Scene,
    variables?: Record<string, string>
  ): Promise<OptimizationResult> {
    try {
      // 构建优化提示词
      const systemPrompt = this.buildSystemPrompt(profile, scene);
      const userPrompt = this.buildUserPrompt(original, variables);

      const { text, usage } = await generateText({
        model: this.provider('gemini-1.5-pro'),
        system: systemPrompt,
        prompt: userPrompt,
        temperature: 0.7,
        maxTokens: 2000,
      });

      return {
        optimized: text,
        metadata: {
          tokensUsed: usage?.totalTokens || 0,
          processingTime: Date.now(),
          quality: this.calculateQuality(original, text)
        }
      };
    } catch (error) {
      throw new AIServiceError('优化失败', error);
    }
  }

  private buildSystemPrompt(profile: Profile, scene?: Scene): string {
    let prompt = `你是一个专业的提示词优化专家。

用户画像：
- 角色：${profile.role}
- 技能：${profile.skills.join(', ')}
- 语调：${profile.tone}

请根据以上用户画像优化提示词，使其更加精确、有效。`;

    if (scene) {
      prompt += `\n\n场景模板：${scene.template}`;
    }

    return prompt;
  }

  private buildUserPrompt(original: string, variables?: Record<string, string>): string {
    let prompt = `请优化以下提示词：\n\n${original}`;
    
    if (variables && Object.keys(variables).length > 0) {
      prompt += '\n\n变量值：\n';
      Object.entries(variables).forEach(([key, value]) => {
        prompt += `- ${key}: ${value}\n`;
      });
    }

    return prompt;
  }

  private calculateQuality(original: string, optimized: string): number {
    // 简单的质量评估算法
    const lengthRatio = optimized.length / original.length;
    const wordCountRatio = optimized.split(' ').length / original.split(' ').length;
    
    // 基于长度和词汇丰富度的简单评分
    return Math.min(100, Math.max(0, (lengthRatio + wordCountRatio) * 50));
  }
}

interface OptimizationResult {
  optimized: string;
  metadata: {
    tokensUsed: number;
    processingTime: number;
    quality: number;
  };
}

export class AIServiceError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'AIServiceError';
  }
}
```

### 优化引擎设计

```typescript
// optimization-engine.ts
export class OptimizationEngine {
  constructor(
    private aiService: AIService,
    private db: PromptSnapDB
  ) {}

  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {
    const startTime = Date.now();

    try {
      // 获取用户画像和场景模板
      const profile = await this.db.profiles.get(request.profileId);
      const scene = request.sceneId ? await this.db.scenes.get(request.sceneId) : undefined;

      if (!profile) {
        throw new Error('未找到指定的用户画像');
      }

      // 执行AI优化
      const result = await this.aiService.optimizePrompt(
        request.original,
        profile,
        scene,
        request.variables
      );

      // 保存优化历史
      const optimization: Optimization = {
        id: crypto.randomUUID(),
        original: request.original,
        optimized: result.optimized,
        sceneId: request.sceneId,
        profileId: request.profileId,
        modelUsed: 'gemini-1.5-pro',
        timestamp: new Date(),
        metadata: {
          ...result.metadata,
          processingTime: Date.now() - startTime
        }
      };

      await this.db.optimizations.add(optimization);

      return {
        ...result,
        optimizationId: optimization.id
      };

    } catch (error) {
      console.error('优化过程中发生错误:', error);
      throw error;
    }
  }

  async getOptimizationHistory(limit = 50): Promise<Optimization[]> {
    return await this.db.optimizations
      .orderBy('timestamp')
      .reverse()
      .limit(limit)
      .toArray();
  }

  async reoptimize(optimizationId: string): Promise<OptimizationResult> {
    const original = await this.db.optimizations.get(optimizationId);
    if (!original) {
      throw new Error('未找到原始优化记录');
    }

    return this.optimize({
      original: original.original,
      profileId: original.profileId!,
      sceneId: original.sceneId
    });
  }
}

interface OptimizationRequest {
  original: string;
  profileId: string;
  sceneId?: string;
  variables?: Record<string, string>;
}
```

## 用户界面设计

### 主要页面布局

#### 1. 优化页面 (/)
```typescript
// pages/OptimizePage.tsx
export function OptimizePage() {
  return (
    <div className="container mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <Card>
          <CardHeader>
            <CardTitle>提示词优化</CardTitle>
          </CardHeader>
          <CardContent>
            <OptimizeForm />
          </CardContent>
        </Card>

        {/* 结果区域 */}
        <Card>
          <CardHeader>
            <CardTitle>优化结果</CardTitle>
          </CardHeader>
          <CardContent>
            <ResultDisplay />
          </CardContent>
        </Card>
      </div>

      {/* 配置区域 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <ProfileSelector />
        <TemplateSelector />
      </div>
    </div>
  );
}
```

#### 2. 提示词仓库页面 (/library)
```typescript
// pages/LibraryPage.tsx
export function LibraryPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">提示词仓库</h1>
        <Button onClick={() => setShowEditor(true)}>
          <Plus className="w-4 h-4 mr-2" />
          新建提示词
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 搜索和筛选 */}
        <div className="lg:col-span-1">
          <SearchBar />
          <TagFilter />
        </div>

        {/* 提示词列表 */}
        <div className="lg:col-span-3">
          <PromptList />
        </div>
      </div>

      <PromptEditor />
    </div>
  );
}
```

### 核心组件实现

#### 优化表单组件 (使用 Zod 验证)
```typescript
// components/OptimizeForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// 优化请求 Schema
const OptimizeFormSchema = z.object({
  original: z.string()
    .min(1, '请输入要优化的提示词')
    .max(10000, '提示词内容过长'),
  profileId: z.string().uuid('请选择有效的个人画像'),
  sceneId: z.string().uuid().optional(),
  variables: z.record(z.string()).optional(),
});

type OptimizeFormData = z.infer<typeof OptimizeFormSchema>;

export function OptimizeForm() {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const { optimize } = useOptimization();
  
  const form = useForm<OptimizeFormData>({
    resolver: zodResolver(OptimizeFormSchema),
    defaultValues: {
      original: '',
      profileId: '',
      sceneId: undefined,
      variables: {},
    },
  });

  const handleOptimize = async (data: OptimizeFormData) => {
    setIsOptimizing(true);
    try {
      // 数据已经通过 Zod 验证，可以安全使用
      await optimize(data);
      toast.success('优化完成');
    } catch (error) {
      if (error instanceof z.ZodError) {
        // 处理验证错误
        error.errors.forEach((err) => {
          toast.error(err.message);
        });
      } else {
        toast.error('优化失败，请重试');
      }
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(handleOptimize)} className="space-y-4">
      <div>
        <Label htmlFor="original">提示词内容</Label>
        <Textarea
          id="original"
          placeholder="输入您要优化的提示词..."
          {...form.register('original')}
          rows={6}
          className="resize-none"
        />
        {form.formState.errors.original && (
          <p className="text-sm text-red-500 mt-1">
            {form.formState.errors.original.message}
          </p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="profileId">个人画像</Label>
          <ProfileSelector
            value={form.watch('profileId')}
            onValueChange={(value) => form.setValue('profileId', value)}
          />
          {form.formState.errors.profileId && (
            <p className="text-sm text-red-500 mt-1">
              {form.formState.errors.profileId.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="sceneId">场景模板 (可选)</Label>
          <SceneSelector
            value={form.watch('sceneId')}
            onValueChange={(value) => form.setValue('sceneId', value)}
          />
        </div>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          {form.watch('original')?.length || 0} 字符
        </div>
        
        <Button 
          type="submit"
          disabled={!form.formState.isValid || isOptimizing}
        >
          {isOptimizing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              优化中...
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4 mr-2" />
              优化提示词
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

// 个人画像编辑表单示例
const ProfileFormSchema = ProfileSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

type ProfileFormData = z.infer<typeof ProfileFormSchema>;

export function ProfileEditForm({ profile, onSave }: {
  profile?: Profile;
  onSave: (data: ProfileFormData) => Promise<void>;
}) {
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(ProfileFormSchema),
    defaultValues: profile ? {
      name: profile.name,
      role: profile.role,
      skills: profile.skills,
      tone: profile.tone,
      isActive: profile.isActive,
    } : {
      name: '',
      role: '',
      skills: [],
      tone: '',
      isActive: false,
    },
  });

  const handleSubmit = async (data: ProfileFormData) => {
    try {
      await onSave(data);
      toast.success(profile ? '画像更新成功' : '画像创建成功');
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          toast.error(err.message);
        });
      } else {
        toast.error('保存失败，请重试');
      }
    }
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">画像名称</Label>
        <Input
          id="name"
          {...form.register('name')}
          placeholder="例如：前端开发者"
        />
        {form.formState.errors.name && (
          <p className="text-sm text-red-500 mt-1">
            {form.formState.errors.name.message}
          </p>
        )}
      </div>

      <div>
        <Label htmlFor="role">角色描述</Label>
        <Input
          id="role"
          {...form.register('role')}
          placeholder="例如：专业的前端开发工程师"
        />
        {form.formState.errors.role && (
          <p className="text-sm text-red-500 mt-1">
            {form.formState.errors.role.message}
          </p>
        )}
      </div>

      {/* 其他字段... */}
      
      <Button type="submit" disabled={!form.formState.isValid}>
        {profile ? '更新画像' : '创建画像'}
      </Button>
    </form>
  );
}
```

## 错误处理和数据恢复

### 数据完整性保护

```typescript
// services/data-integrity.ts
export class DataIntegrityService {
  constructor(private dal: DataAccessLayer) {}

  /**
   * 检查数据库完整性
   */
  async checkDatabaseIntegrity(): Promise<IntegrityCheckResult> {
    const result: IntegrityCheckResult = {
      isHealthy: true,
      issues: [],
      recommendations: []
    };

    try {
      // 检查表结构
      await this.checkTableStructure(result);
      
      // 检查数据一致性
      await this.checkDataConsistency(result);
      
      // 检查索引完整性
      await this.checkIndexIntegrity(result);
      
      // 检查外键约束
      await this.checkForeignKeyConstraints(result);

    } catch (error) {
      result.isHealthy = false;
      result.issues.push({
        type: 'CRITICAL',
        message: `数据库完整性检查失败: ${error.message}`,
        recommendation: '请尝试从备份恢复数据'
      });
    }

    return result;
  }

  /**
   * 自动修复数据问题
   */
  async autoRepairData(): Promise<RepairResult> {
    const repairResult: RepairResult = {
      success: true,
      repairedIssues: [],
      unresolvedIssues: []
    };

    const integrityCheck = await this.checkDatabaseIntegrity();
    
    for (const issue of integrityCheck.issues) {
      try {
        switch (issue.type) {
          case 'MISSING_INDEX':
            await this.repairMissingIndex(issue);
            repairResult.repairedIssues.push(issue);
            break;
          case 'ORPHANED_RECORD':
            await this.repairOrphanedRecord(issue);
            repairResult.repairedIssues.push(issue);
            break;
          case 'CORRUPTED_JSON':
            await this.repairCorruptedJson(issue);
            repairResult.repairedIssues.push(issue);
            break;
          default:
            repairResult.unresolvedIssues.push(issue);
        }
      } catch (error) {
        repairResult.unresolvedIssues.push({
          ...issue,
          repairError: error.message
        });
      }
    }

    repairResult.success = repairResult.unresolvedIssues.length === 0;
    return repairResult;
  }

  /**
   * 创建数据快照
   */
  async createDataSnapshot(): Promise<string> {
    const timestamp = new Date().toISOString();
    const snapshot = await this.dal.exportData({
      includeProfiles: true,
      includeScenes: true,
      includePrompts: true,
      includeHistory: false, // 历史记录不包含在快照中
      includeSettings: true
    });

    const snapshotData = {
      ...snapshot,
      snapshotId: crypto.randomUUID(),
      createdAt: timestamp,
      type: 'auto-snapshot'
    };

    return JSON.stringify(snapshotData, null, 2);
  }

  private async checkTableStructure(result: IntegrityCheckResult): Promise<void> {
    // 检查必要的表是否存在
    const requiredTables = ['profiles', 'scenes', 'prompts', 'optimizations', 'settings'];
    
    for (const table of requiredTables) {
      const exists = await this.dal.tableExists(table);
      if (!exists) {
        result.isHealthy = false;
        result.issues.push({
          type: 'MISSING_TABLE',
          message: `缺少必要的数据表: ${table}`,
          recommendation: '重新初始化数据库'
        });
      }
    }
  }

  private async checkDataConsistency(result: IntegrityCheckResult): Promise<void> {
    // 检查 JSON 字段的有效性
    const jsonFields = [
      { table: 'profiles', field: 'skills' },
      { table: 'scenes', field: 'variables' },
      { table: 'prompts', field: 'tags' },
      { table: 'optimizations', field: 'metadata' }
    ];

    for (const { table, field } of jsonFields) {
      const invalidRecords = await this.findInvalidJsonRecords(table, field);
      if (invalidRecords.length > 0) {
        result.isHealthy = false;
        result.issues.push({
          type: 'CORRUPTED_JSON',
          message: `表 ${table} 中的 ${field} 字段包含无效 JSON`,
          data: { table, field, count: invalidRecords.length },
          recommendation: '修复或重置受影响的记录'
        });
      }
    }
  }
}

// 类型定义
interface IntegrityCheckResult {
  isHealthy: boolean;
  issues: DataIssue[];
  recommendations: string[];
}

interface DataIssue {
  type: 'CRITICAL' | 'MISSING_TABLE' | 'MISSING_INDEX' | 'ORPHANED_RECORD' | 'CORRUPTED_JSON';
  message: string;
  recommendation: string;
  data?: any;
  repairError?: string;
}

interface RepairResult {
  success: boolean;
  repairedIssues: DataIssue[];
  unresolvedIssues: DataIssue[];
}
```

### 自动备份策略

```typescript
// services/backup-service.ts
export class BackupService {
  private backupInterval: NodeJS.Timeout | null = null;

  constructor(
    private dal: DataAccessLayer,
    private integrityService: DataIntegrityService
  ) {}

  /**
   * 启动自动备份
   */
  startAutoBackup(intervalHours: number = 24): void {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }

    this.backupInterval = setInterval(async () => {
      try {
        await this.createScheduledBackup();
      } catch (error) {
        console.error('自动备份失败:', error);
      }
    }, intervalHours * 60 * 60 * 1000);
  }

  /**
   * 停止自动备份
   */
  stopAutoBackup(): void {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }
  }

  /**
   * 创建计划备份
   */
  private async createScheduledBackup(): Promise<void> {
    // 检查数据完整性
    const integrityCheck = await this.integrityService.checkDatabaseIntegrity();
    
    if (!integrityCheck.isHealthy) {
      console.warn('数据完整性检查失败，跳过自动备份');
      return;
    }

    // 创建备份
    const backupContent = await this.dal.createBackup();
    
    // 保存备份文件（根据平台不同实现）
    await this.saveBackupFile(backupContent);
    
    // 清理旧备份
    await this.cleanupOldBackups();
  }

  private async saveBackupFile(content: string): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `prompt-snap-auto-backup-${timestamp}.json`;
    
    // 在桌面应用中使用 Tauri 文件系统 API
    // 在网页端使用 IndexedDB 存储
    if (typeof window !== 'undefined' && 'indexedDB' in window) {
      // 网页端实现
      await this.saveToIndexedDB(filename, content);
    } else {
      // 桌面端实现
      await this.saveToFileSystem(filename, content);
    }
  }
}
```

### 错误类型定义
```typescript
// types/errors.ts
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR'
}

export class AppError extends Error {
  constructor(
    public type: ErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}
```

### 全局错误处理
```typescript
// hooks/useErrorHandler.ts
export function useErrorHandler() {
  const handleError = useCallback((error: Error | AppError) => {
    console.error('应用错误:', error);

    if (error instanceof AppError) {
      switch (error.type) {
        case ErrorType.NETWORK_ERROR:
          toast.error('网络连接失败，请检查网络设置');
          break;
        case ErrorType.AI_SERVICE_ERROR:
          toast.error('AI服务暂时不可用，请稍后重试');
          break;
        case ErrorType.DATABASE_ERROR:
          toast.error('数据保存失败，请重试');
          break;
        default:
          toast.error(error.message || '发生未知错误');
      }
    } else {
      toast.error('发生未知错误，请重试');
    }
  }, []);

  return { handleError };
}
```

## 测试策略

### 单元测试
- 使用 Vitest 进行单元测试
- 测试覆盖率目标：80%以上
- 重点测试业务逻辑和工具函数

### 集成测试
- 使用 React Testing Library 测试组件交互
- 测试 AI 服务集成
- 测试数据库操作

### E2E 测试
- 使用 Playwright 进行端到端测试
- 测试关键用户流程
- 测试跨浏览器兼容性

### 测试配置示例
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
});
```

## 开发阶段规划

### 阶段一：网页端核心功能开发 (Week 1-3)
**目标**: 开发网页端版本，验证核心功能和用户体验

**技术栈**:
- React 19 + TypeScript + Vite
- IndexedDB + Dexie.js (数据存储)
- shadcn/ui + Tailwind CSS
- Vercel AI SDK + Google Provider
- nunjucks (模板引擎)

**功能范围**:
- 基础的提示词优化功能
- 个人画像和场景模板管理
- 提示词仓库和搜索功能
- 优化历史记录
- 数据导出/导入功能
- 响应式网页界面

**开发重点**:
- 快速原型开发和功能验证
- 用户体验优化和界面设计
- 数据模型设计和验证
- AI 服务集成和优化
- 完整的错误处理机制

**设计决策理由**:
- **快速迭代**: 网页端开发周期短，便于快速验证功能
- **技术熟悉度**: 团队对 Web 技术栈更熟悉，减少学习成本
- **用户反馈**: 可以快速部署给用户测试，收集反馈
- **代码复用**: 后续迁移到桌面应用时可以复用大部分代码

### 阶段二：桌面应用迁移和系统集成 (Week 4-6)
**目标**: 将网页端功能迁移到 Tauri 桌面应用，添加系统级功能

**技术迁移**:
- 数据存储：IndexedDB → SQLite
- 系统集成：添加 Tauri APIs
- 打包部署：Web → 桌面应用

**新增功能**:
- 全局快捷键支持（双击 Ctrl 触发优化）
- 系统托盘集成和菜单
- 剪贴板自动读写功能
- 窗口管理和状态保持
- 快速优化弹窗
- 应用自启动配置

**迁移策略**:
- 保持组件和业务逻辑不变
- 创建数据迁移工具（IndexedDB → SQLite）
- 添加系统集成适配层
- 渐进式功能迁移和测试

### 阶段三：优化、测试和发布 (Week 7)
**目标**: 性能优化、全面测试和发布准备

**优化内容**:
- 应用启动速度优化
- 数据库查询性能优化
- 内存使用优化
- 用户体验改进和动画效果
- 错误处理完善和用户反馈
- 自动更新机制

**测试和发布**:
- 单元测试和集成测试
- macOS 平台测试和优化
- 应用签名和公证
- 安装包制作和分发
- 用户文档和使用指南

### 开发环境要求

由于使用 SQLite 和 Tauri 系统集成功能，开发环境需要：

1. **Rust 环境**: 
   - 安装 Rust 和 Cargo
   - 配置 Tauri CLI

2. **系统依赖**:
   - macOS: Xcode Command Line Tools
   - 代码签名证书（用于发布）

3. **开发工具**:
   - VS Code + Tauri 扩展
   - Rust Analyzer
   - SQLite 数据库查看工具

### 开发环境配置

#### 阶段一：网页端开发环境

**项目初始化**:
```bash
# 创建 React + TypeScript 项目
npm create vite@latest prompt-snap -- --template react-ts
cd prompt-snap

# 安装依赖
bun install
```

```json
// package.json (网页端版本)
{
  "name": "prompt-snap",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "type-check": "tsc --noEmit",
    "lint": "biome check .",
    "format": "biome format --write .",
    "test": "vitest",
    "test:coverage": "vitest --coverage"
  },
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-router-dom": "^6.20.0",
    "@ai-sdk/google": "^1.0.0",
    "ai": "^4.0.0",
    "zod": "^3.22.0",
    "dexie": "^4.0.0",
    "nunjucks": "^3.2.4",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-separator": "^1.0.3",
    "@radix-ui/react-slot": "^1.0.2",
    "@radix-ui/react-switch": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-textarea": "^1.0.0",
    "@radix-ui/react-toast": "^1.1.5",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "lucide-react": "^0.300.0",
    "react-hook-form": "^7.48.0",
    "@hookform/resolvers": "^3.3.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@types/nunjucks": "^3.2.6",
    "@vitejs/plugin-react": "^4.2.0",
    "typescript": "^5.3.0",
    "vite": "^5.0.0",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32",
    "@biomejs/biome": "^1.4.0",
    "vitest": "^1.0.0",
    "@vitest/coverage-v8": "^1.0.0",
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0"
  }
}
```

**IndexedDB 数据库配置**:
```typescript
// database/web-db.ts
import Dexie, { Table } from 'dexie';
import { Profile, Scene, Prompt, Optimization, AppSettings } from '../schemas';

export class PromptSnapDB extends Dexie {
  profiles!: Table<Profile>;
  scenes!: Table<Scene>;
  prompts!: Table<Prompt>;
  optimizations!: Table<Optimization>;
  settings!: Table<AppSettings>;

  constructor() {
    super('PromptSnapDB');
    
    this.version(1).stores({
      profiles: 'id, name, isActive, createdAt',
      scenes: 'id, title, category, isBuiltIn, createdAt',
      prompts: 'id, title, isFavorite, sceneId, profileId, createdAt',
      optimizations: 'id, timestamp, profileId, sceneId',
      settings: 'id'
    });

    // 添加全文搜索支持
    this.prompts.hook('creating', (primKey, obj, trans) => {
      // 为搜索创建索引
      obj.searchIndex = `${obj.title} ${obj.content} ${obj.tags.join(' ')}`.toLowerCase();
    });
  }

  // 全文搜索方法
  async searchPrompts(query: string, limit = 50): Promise<Prompt[]> {
    const searchTerm = query.toLowerCase();
    return await this.prompts
      .filter(prompt => 
        prompt.title.toLowerCase().includes(searchTerm) ||
        prompt.content.toLowerCase().includes(searchTerm) ||
        prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
      .limit(limit)
      .toArray();
  }
}

export const db = new PromptSnapDB();
```

#### 阶段二：Tauri 桌面应用开发环境

**项目初始化**:
```bash
# 创建 Tauri 项目
npm create tauri-app@latest prompt-snap
cd prompt-snap

# 选择配置：
# - Package manager: npm
# - UI template: React + TypeScript
# - UI flavor: TypeScript
```

```json
// package.json
{
  "name": "prompt-snap",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "tauri dev",
    "build": "tauri build",
    "preview": "vite preview",
    "type-check": "tsc --noEmit",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
  },
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-router-dom": "^6.20.0",
    "@ai-sdk/google": "^1.0.0",
    "ai": "^4.0.0",
    "zod": "^3.22.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-separator": "^1.0.3",
    "@radix-ui/react-slot": "^1.0.2",
    "@radix-ui/react-switch": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-textarea": "^1.0.0",
    "@radix-ui/react-toast": "^1.1.5",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "lucide-react": "^0.300.0",
    "react-hook-form": "^7.48.0",
    "@hookform/resolvers": "^3.3.0"
  },
  "devDependencies": {
    "@tauri-apps/cli": "^2.0.0",
    "@tauri-apps/api": "^2.0.0",
    "@tauri-apps/plugin-global-shortcut": "^2.0.0",
    "@tauri-apps/plugin-clipboard-manager": "^2.0.0",
    "@tauri-apps/plugin-sql": "^2.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.2.0",
    "typescript": "^5.3.0",
    "vite": "^5.0.0",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32",
    "eslint": "^8.55.0",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5"
  }
}
```

#### Tauri 配置
```json
// src-tauri/tauri.conf.json
{
  "productName": "Prompt Snap",
  "version": "1.0.0",
  "identifier": "com.promptsnap.app",
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "app": {
    "windows": [
      {
        "title": "Prompt Snap",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600,
        "center": true,
        "resizable": true
      }
    ],
    "systemTray": {
      "iconPath": "icons/icon.png",
      "iconAsTemplate": true,
      "menuOnLeftClick": false
    }
  },
  "bundle": {
    "active": true,
    "targets": ["dmg", "app"],
    "identifier": "com.promptsnap.app",
    "icon": ["icons/32x32.png", "icons/128x128.png", "icons/icon.icns"],
    "resources": [],
    "copyright": "",
    "category": "DeveloperTool",
    "shortDescription": "AI-powered prompt optimization tool",
    "longDescription": "Prompt Snap is an intelligent prompt optimization tool that helps developers and content creators improve their AI prompts."
  },
  "plugins": {
    "global-shortcut": {
      "all": true
    },
    "clipboard-manager": {
      "all": true
    },
    "sql": {
      "preload": ["sqlite:prompt_snap.db"]
    }
  }
}
```

## 性能优化

### 前端性能优化
1. **代码分割**: 使用 React.lazy 和 Suspense 实现路由级别的代码分割
2. **虚拟滚动**: 对于大量数据列表使用虚拟滚动
3. **缓存策略**: 实现智能缓存机制，减少重复请求
4. **图片优化**: 使用 WebP 格式和懒加载

### 数据库性能优化
1. **索引优化**: 为常用查询字段添加 B-tree 索引
2. **全文搜索**: 使用 SQLite FTS5 实现高效的全文搜索
3. **查询优化**: 使用 EXPLAIN QUERY PLAN 优化复杂查询
4. **连接池**: 实现数据库连接池管理
5. **事务管理**: 批量操作使用事务提升性能
6. **分页查询**: 使用 LIMIT 和 OFFSET 实现高效分页

### AI 服务优化
1. **请求缓存**: 缓存相似的优化请求结果
2. **批量处理**: 支持批量优化功能
3. **流式响应**: 使用流式响应提升用户体验

## 安全考虑

### 数据安全
1. **本地存储**: 敏感数据仅存储在本地 IndexedDB
2. **API密钥管理**: 安全存储和传输 API 密钥
3. **数据加密**: 对敏感数据进行客户端加密

### 输入验证
1. **XSS防护**: 对用户输入进行严格验证和转义
2. **内容过滤**: 过滤恶意内容和敏感信息
3. **长度限制**: 限制输入内容的长度

### 隐私保护
1. **数据最小化**: 只收集必要的用户数据
2. **匿名化**: 对分析数据进行匿名化处理
3. **用户控制**: 提供数据导出和删除功能
# 项目结构

## 根目录布局

```
prompt-snap/
├── src/                          # 前端源代码
├── src-tauri/                    # Tauri 后端 (Rust)
├── public/                       # 静态资源
├── database/                     # 数据库模式和迁移
├── .kiro/                        # Kiro 配置
└── package.json                  # Node.js 依赖
```

## 前端结构 (`src/`)

```
src/
├── components/                   # 可复用 UI 组件
│   ├── ui/                      # shadcn/ui 组件
│   ├── forms/                   # 表单特定组件
│   ├── layout/                  # 布局组件 (侧边栏、头部)
│   └── common/                  # 通用工具组件
├── pages/                       # 页面组件
│   ├── OptimizePage.tsx         # 主要优化界面
│   ├── LibraryPage.tsx          # 提示词仓库
│   ├── HistoryPage.tsx          # 优化历史
│   ├── TemplatesPage.tsx        # 场景模板管理
│   └── SettingsPage.tsx         # 应用设置
├── hooks/                       # 自定义 React hooks
│   ├── useDatabase.ts           # 数据库操作
│   ├── useAI.ts                 # AI 服务集成
│   └── useSettings.ts           # 设置管理
├── services/                    # 业务逻辑服务
│   ├── ai-service.ts            # AI 优化逻辑
│   ├── database/                # 数据库访问层
│   │   ├── dal.ts              # 数据访问层
│   │   └── migrations.ts        # 数据库迁移
│   └── tauri-system.ts          # 系统集成
├── schemas/                     # Zod 验证模式
│   └── index.ts                 # 所有数据模型和验证
├── types/                       # TypeScript 类型定义
├── utils/                       # 工具函数
├── contexts/                    # React 上下文状态管理
└── App.tsx                      # 主应用组件
```

## 后端结构 (`src-tauri/`)

```
src-tauri/
├── src/
│   ├── main.rs                  # 主 Tauri 应用
│   ├── commands.rs              # Tauri 命令 (剪贴板、快捷键)
│   └── system.rs                # 系统集成逻辑
├── Cargo.toml                   # Rust 依赖
├── tauri.conf.json              # Tauri 配置
└── icons/                       # 应用图标
```

## 数据库结构 (`database/`)

```
database/
├── schema.sql                   # 初始数据库模式
└── migrations/                  # 数据库迁移脚本
    └── 001_initial.sql
```

## 关键架构模式

### 组件组织

- **页面**: 编排数据和布局的顶级路由组件
- **组件**: 按领域组织的可复用 UI 组件
- **Hooks**: 用于数据获取和状态管理的自定义 hooks
- **服务**: 从 UI 组件中分离的业务逻辑

### 数据流

1. **UI 组件** 通过 hooks 触发操作
2. **Hooks** 调用服务方法处理业务逻辑
3. **服务** 使用 DAL 进行数据库操作
4. **DAL** 在数据库操作前使用 Zod 模式验证数据

### 文件命名约定

- **组件**: PascalCase (例如 `OptimizeForm.tsx`)
- **Hooks**: camelCase 带 `use` 前缀 (例如 `useDatabase.ts`)
- **服务**: kebab-case (例如 `ai-service.ts`)
- **类型**: PascalCase (例如 `Profile`, `OptimizationResult`)

### 导入组织

```typescript
// 外部库
import React from "react";
import { z } from "zod";

// 内部服务和 hooks
import { useDatabase } from "@/hooks/useDatabase";
import { aiService } from "@/services/ai-service";

// 组件
import { Button } from "@/components/ui/button";
import { OptimizeForm } from "@/components/forms/OptimizeForm";

// 类型和模式
import type { Profile } from "@/types";
import { ProfileSchema } from "@/schemas";
```

### 状态管理策略

- **本地状态**: 使用 useState 处理组件特定状态
- **共享状态**: 使用 React Context 处理应用级状态 (设置、当前档案)
- **服务器状态**: 使用带适当缓存的自定义 hooks 进行数据库操作
- **表单状态**: 使用 React Hook Form 处理带验证的复杂表单

---
inclusion: always
---

# 开发指南

## 代码规范

### TypeScript & React
- 使用严格的 TypeScript，为函数参数/返回值提供明确类型
- 仅使用带 hooks 的函数组件 - 不使用类组件
- 在 API 边界使用 Zod 模式进行所有数据验证
- 对性能关键组件使用 React.memo()
- 实现适当的错误边界以进行健壮的错误处理

### 文件组织
- 组件: PascalCase (`OptimizeForm.tsx`)
- Hooks: camelCase 带 `use` 前缀 (`useDatabase.ts`)
- 服务: kebab-case (`ai-service.ts`)
- 使用桶导出 (index.ts) 实现清洁导入
- 将业务逻辑与 UI 组件分离

### 导入顺序
```typescript
// 外部库优先
import React from 'react';
import { z } from 'zod';

// 内部服务/hooks
import { useDatabase } from '@/hooks/useDatabase';

// 组件
import { Button } from '@/components/ui/button';

// 类型最后
import type { Profile } from '@/types';
```

## 架构模式

### 数据流
遵循严格的单向流: **UI → Hooks → Services → DAL → SQLite**
- 在边界使用 Zod 模式验证所有数据
- 一致地处理加载/错误状态
- 谨慎使用 React Context - 优先使用本地状态和属性传递

### 数据库操作
- 始终使用参数化查询 (防止 SQL 注入)
- 为多步操作实现适当的事务处理
- 使用 SQLite FTS5 进行全文搜索
- 在应用启动时自动处理迁移

### AI 集成
- 对 Gemini API 调用进行速率限制和适当的错误处理
- 在处理前验证 AI 响应
- 在 AI 不可用时实现回退行为
- 适当缓存结果以提高性能

## 安全与系统集成

### Tauri 最佳实践
- 对所有系统操作使用 Tauri 的安全 API
- 对文件系统访问进行适当的权限处理
- 在应用退出时清理全局快捷键
- 通过适当验证进行安全的剪贴板操作

### 数据验证
- 在数据库操作前清理所有用户输入
- 对所有数据输入使用 Zod 验证模式
- 适当处理敏感数据

## UI/UX 标准

### shadcn/ui 模式
- 遵循已建立的 shadcn/ui 组件模式
- 实现一致的加载状态和用户反馈
- 提供清晰的错误消息和恢复选项
- 确保适当的可访问性 (ARIA 标签、键盘导航)

### 性能
- 对大数据集使用虚拟滚动 (提示词库、历史)
- 对非关键组件进行懒加载
- 通过适当索引优化数据库查询

## 语言与本地化
- 面向用户的内容: 适当时使用中文
- 代码标识符和技术文档: 为保持一致性使用英文
- 注释: 使用最有利于可维护性的语言

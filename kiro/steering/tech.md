# 技术栈

## 框架与运行时

- **桌面框架**: Tauri v2 (Rust 后端 + Web 前端)
- **前端**: React 19 + TypeScript
- **构建工具**: Vite
- **包管理器**: bun

## UI 与样式

- **组件库**: shadcn/ui
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **路由**: React Router v6

## 状态管理与数据

- **状态管理**: React Context + useReducer
- **数据验证**: Zod (模式验证和类型安全)
- **数据库**: SQLite (通过 Tauri SQL 插件)
- **全文搜索**: SQLite FTS5

## AI 集成

- **AI SDK**: Vercel AI SDK
- **AI 提供商**: Google Provider (Gemini 模型)
- **模板引擎**: 基于 Jinja 的模板 (nunjucks 或 jinja-js)

## 系统集成

- **全局快捷键**: Tauri global-shortcut 插件
- **剪贴板**: Tauri clipboard APIs
- **系统托盘**: Tauri system tray APIs
- **文件系统**: Tauri filesystem APIs

## 开发工具

- **代码质量**: @biomejs/biome (替代 ESLint + Prettier)
- **表单**: React Hook Form + Zod 验证

## 常用命令

### 开发

```bash
# 启动开发服务器
bun run tauri dev

# 生产构建
bun run tauri build

# 仅运行前端 (web 模式)
bun run dev

# 类型检查
bun run type-check

# 代码格式化和检查
bun run lint
bun run format
```

### 数据库

```bash
# 数据库迁移在应用启动时自动处理
# 手动重置数据库 (仅开发环境)
rm -rf ~/.local/share/prompt-snap/
```

### 测试

```bash
# 运行单元测试
bun run test

# 运行集成测试
bun run test:integration

# 运行所有测试并生成覆盖率报告
bun run test:coverage
```

## 架构说明

- **数据流**: React 组件 → Context/Hooks → DAL → SQLite
- **验证**: 在 API 边界和表单输入处使用 Zod 模式
- **错误处理**: 全局错误边界 + 特定错误处理
- **性能**: 大列表虚拟滚动，组件懒加载
